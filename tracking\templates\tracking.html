<div class="row page" data-ng-controller="tracking">
    <!--SN life cycles side nav start-->
    <md-sidenav class="md-sidenav-right" md-component-id="closeEventsDisabled"
    md-whiteframe="4" md-disable-close-events>
        <md-toolbar class="md-table-toolbar md-default">
            <div class="md-toolbar-tools">
                <span class="text-warning">SN Results</span>
                <div flex></div>
                <md-icon class="excel_icon" md-svg-src="../assets/images/excel.svg" ng-click="GetSerialExcel(assetcompletedetails.SerialNumber,assetcompletedetails.AssetScanID)"></md-icon>
                <i class="material-icons text-danger" ng-click="toggleSidenav()">close</i>                       
            </div>
        </md-toolbar>
        <md-content layout-margin="">
            <form>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>SN</label>
                        <p class="form-control-static">{{assetcompletedetails.SerialNumber}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Container ID</label>
                        <p class="form-control-static">{{assetcompletedetails.idPallet}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>MPN</label>
                        <p class="form-control-static">{{assetcompletedetails.UniversalModelNumber}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>disposition</label>
                        <p class="form-control-static">{{assetcompletedetails.disposition}}</p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group">
                        <label>Sanitization seal ID</label>
                        <p class="form-control-static">{{assetcompletedetails.sanitization_seal_id}}</p>
                    </div>
                </div>

                <!-- <div class="col-md-4">
                    <div class="form-group">
                        <label>Business Rule ID</label>
                        <p class="form-control-static">
                            <span ng-show="assetcompletedetails.rule_name != ''">{{assetcompletedetails.rule_name}}</span>
                            <span ng-show="assetcompletedetails.rule_name == '' || assetcompletedetails.rule_name == null">Disposition Auto Assigned</span>
                        </p>
                    </div>
                </div> -->
            </form>
            <div style="clear: both;"></div>
            <h5 class="text-success" style="margin-top: 22px;">Life Cycle</h5>
            <md-table-container>
                <table md-table class="table">
                    <thead md-head>
                        <tr md-row class="bg-grey">                                 
                            <th md-column>Action</th>
                            <th md-column style="min-width:220px;">Modified Date</th>    
                            <th md-column>Modified By</th>                                                                                                                        
                        </tr>
                    </thead>
                    <tbody md-body>
                        <tr md-row ng-repeat="itemtrack in assetcompletetracking">
                            <td md-cell>{{itemtrack.Action}}</td>
                            <td md-cell>{{itemtrack.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                            <td md-cell>{{itemtrack.FirstName}}{{itemtrack.LastName}}</td>
                        </tr>
                    </tbody>
                </table>
            </md-table-container>
        </md-content>
    </md-sidenav>
    <!--SN life cycles side nav end-->
    <!--Ticket life cycles side nav start-->
    <md-sidenav class="md-sidenav-right" md-component-id="closeEventsDisabled2"
    md-whiteframe="4" md-disable-close-events>
        <md-toolbar class="md-table-toolbar md-default">
            <div class="md-toolbar-tools">
                <span class="text-warning">Ticket Results</span>
                <div flex></div>
                <md-icon class="excel_icon" md-svg-src="../assets/images/excel.svg" ng-click="GetTicketExcel(loadcompletedetails.LoadId)"></md-icon>
                <i class="material-icons text-danger" ng-click="toggleSidenav2()">close</i>                       
            </div>
        </md-toolbar>
        <md-content layout-margin="">
            <form>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Ticket ID</label>
                        <p class="form-control-static">{{loadcompletedetails.LoadId}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Source</label>
                        <p class="form-control-static">{{loadcompletedetails.CustomerName}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Facility</label>
                        <p class="form-control-static">{{loadcompletedetails.FacilityName}}</p>
                    </div>
                </div>
            </form>
            <div style="clear: both;"></div>
            <h5 class="text-success" style="margin-top: 22px;">Life Cycle</h5>
            <md-table-container>
                <table md-table class="table">
                    <thead md-head>
                        <tr md-row class="bg-grey">                                 
                            <th md-column>Action</th>
                            <th md-column style="min-width:220px;">Modified Date</th>    
                            <th md-column>Modified  By</th>                                                                                                                        
                        </tr>
                    </thead>
                    <tbody md-body>
                        <tr md-row ng-repeat="itemtrack in loadcompletetracking">
                            <td md-cell>{{itemtrack.Action}}</td>
                            <td md-cell>{{itemtrack.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                            <td md-cell>{{itemtrack.FirstName}}{{itemtrack.LastName}}</td>
                        </tr>
                    </tbody>
                </table>
            </md-table-container>
        </md-content>
    </md-sidenav>
    <!--Ticket life cycles side nav end-->

    <!--Container life cycles side nav start-->
    <md-sidenav class="md-sidenav-right" md-component-id="closeEventsDisabled3"
    md-whiteframe="4" md-disable-close-events>
        <md-toolbar class="md-table-toolbar md-default">
            <div class="md-toolbar-tools">
                <span class="text-warning">Container Results</span>
                <div flex></div>
                <md-icon class="excel_icon" md-svg-src="../assets/images/excel.svg" ng-click="GetContainerExcel(palletcompletedetails.idPallet)"></md-icon>
                <i class="material-icons text-danger" ng-click="toggleSidenav3()">close</i>                       
            </div>
        </md-toolbar>
        <md-content layout-margin="">
            <form>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Container ID</label>
                        <p class="form-control-static">{{palletcompletedetails.idPallet}}</p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group">
                        <label>Source</label>
                        <p class="form-control-static">{{palletcompletedetails.CustomerName}}</p>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="form-group">
                        <label>Seal ID</label>
                        <p class="form-control-static">	{{palletcompletedetails.SealNo1}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Location</label>
                        <p class="form-control-static">{{palletcompletedetails.LocationName}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Received Date</label>
                        <p class="form-control-static">	{{palletcompletedetails.ReceivedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</p>
                    </div>
                </div>
            </form>
            <div style="clear: both;"></div>
            <h5 class="text-success" style="margin-top: 22px;">Life Cycle</h5>
            <md-table-container>
                <table md-table class="table">
                    <thead md-head>
                        <tr md-row class="bg-grey">                                 
                            <th md-column>Action</th>
                            <th md-column style="min-width:220px;">Modified Date</th>    
                            <th md-column>Modified  By</th>                                                                                                                        
                        </tr>
                    </thead>
                    <tbody md-body>
                        <tr md-row ng-repeat="itemtrack in palletcompletetracking">
                            <td md-cell>{{itemtrack.Action}}</td>
                            <td md-cell>{{itemtrack.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                            <td md-cell>{{itemtrack.FirstName}}{{itemtrack.LastName}}</td>
                        </tr>
                    </tbody>
                </table>
            </md-table-container>
        </md-content>
    </md-sidenav>
    <!--Container life cycles side nav end-->




    <!--Server life cycles side nav start-->
    <md-sidenav class="md-sidenav-right" md-component-id="closeEventsDisabled11"
    md-whiteframe="4" md-disable-close-events>
        <md-toolbar class="md-table-toolbar md-default">
            <div class="md-toolbar-tools">
                <span class="text-warning">SN Results</span>
                <div flex></div>
                <md-icon class="excel_icon" md-svg-src="../assets/images/excel.svg" ng-click="GetServerExcel(ServerCompleteDetails.ServerSerialNumber)"></md-icon>
                <i class="material-icons text-danger" ng-click="toggleSidenav11()">close</i>                       
            </div>
        </md-toolbar>
        <md-content layout-margin="">
            <form>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>SN</label>
                        <p class="form-control-static">{{ServerCompleteDetails.SerialNumber}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Rack ID</label>
                        <p class="form-control-static">{{ServerCompleteDetails.idPallet}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>MPN</label>
                        <p class="form-control-static">{{ServerCompleteDetails.UniversalModelNumber}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Disposition</label>
                        <p class="form-control-static">{{ServerCompleteDetails.disposition}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Type</label>
                        <p class="form-control-static">{{ServerCompleteDetails.parttype}}</p>
                    </div>
                </div>

                <!-- <div class="col-md-4">
                    <div class="form-group">
                        <label>Business Rule ID</label>
                        <p class="form-control-static">
                            <span ng-show="ServerCompleteDetails.rule_name != ''">{{ServerCompleteDetails.rule_name}}</span>
                            <span ng-show="ServerCompleteDetails.rule_name == '' || ServerCompleteDetails.rule_name == null">Disposition Auto Assigned</span>
                        </p>
                    </div>
                </div> -->

                <!-- <div class="col-md-4">
                    <div class="form-group">
                        <label>Current BIN</label>
                        <p class="form-control-static">{{ServerCompleteDetails.BinName}}</p>
                    </div>
                </div> -->
            </form>
            <div style="clear: both;"></div>            
            <h5 class="text-success" style="margin-top: 22px;">Life Cycle</h5>
            <div >
                <md-table-container>
                    <table md-table class="table">
                        <thead md-head>
                            <tr md-row class="bg-grey">                                 
                                <th md-column>Action</th>
                                <th md-column style="min-width:220px;">Modified Date</th>    
                                <th md-column>Modified By</th>                                                                                                                        
                            </tr>
                        </thead>
                        <tbody md-body>
                            <tr md-row ng-repeat="itemtrack in ServerLifeCycle">
                                <td md-cell>{{itemtrack.Action}}</td>
                                <td md-cell>{{itemtrack.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                                <td md-cell>{{itemtrack.FirstName}}{{itemtrack.LastName}}</td>
                            </tr>
                        </tbody>
                    </table>
                </md-table-container>                    
            </div>
        </md-content>
    </md-sidenav>
    <!--Server life cycles side nav end-->




    <!--Media life cycles side nav start-->
    <md-sidenav class="md-sidenav-right" md-component-id="closeEventsDisabled10"
    md-whiteframe="4" md-disable-close-events>
        <md-toolbar class="md-table-toolbar md-default">
            <div class="md-toolbar-tools">
                <span class="text-warning">SN Results</span>
                <div flex></div>
                <md-icon class="excel_icon" md-svg-src="../assets/images/excel.svg" ng-click="GetSerialExcel(mediacompletedetails.MediaSerialNumber,mediacompletedetails.AssetScanID)"></md-icon>
                <i class="material-icons text-danger" ng-click="toggleSidenav10()">close</i>                       
            </div>
        </md-toolbar>
        <md-content layout-margin="">
            <form>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>SN</label>
                        <p class="form-control-static">{{mediacompletedetails.SerialNumber}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Container ID</label>
                        <p class="form-control-static">{{mediacompletedetails.idPallet}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>MPN</label>
                        <p class="form-control-static">{{mediacompletedetails.UniversalModelNumber}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Disposition</label>
                        <p class="form-control-static">{{mediacompletedetails.disposition}}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Type</label>
                        <p class="form-control-static">{{mediacompletedetails.parttype}}</p>
                    </div>
                </div>

                <!-- <div class="col-md-4">
                    <div class="form-group">
                        <label>Business Rule ID</label>
                        <p class="form-control-static">
                            <span ng-show="mediacompletedetails.rule_name != ''">{{mediacompletedetails.rule_name}}</span>
                            <span ng-show="mediacompletedetails.rule_name == '' || mediacompletedetails.rule_name == null">Disposition Auto Assigned</span>
                        </p>
                    </div>
                </div> -->
            </form>
            <div style="clear: both;"></div>

            <div>                
                <md-table-container>
                    <table md-table class="table">
                        <thead md-head>
                            <tr md-row class="bg-grey">                                 
                                <th md-column>Action</th>
                                <th md-column style="min-width:220px;">Modified Date</th>    
                                <th md-column>Modified By</th>                                                                                                                        
                            </tr>
                        </thead>
                        <tbody md-body>
                            <tr md-row ng-repeat="itemtrack in mediacompletetracking">
                                <td md-cell>{{itemtrack.Action}}</td>
                                <td md-cell>{{itemtrack.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                                <td md-cell>{{itemtrack.FirstName}}{{itemtrack.LastName}}</td>
                            </tr>
                        </tbody>
                    </table>
                </md-table-container>
            </div>            

        </md-content>
    </md-sidenav>
    <!--Media life cycles side nav end-->


    <div class="col-md-12">
        <article class="article">
            <md-card class="no-margin-h"> 
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Search Track Results</span> 
                        <div flex></div>
                            <a ng-click="ExportTrackRecords(TrackSN1)" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                <md-icon class="excel_icon mr-5" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                            </a>                         
                    </div>
                </md-toolbar>
                <div class="row">
                    <form>
                        <div class="col-md-12">

                            <div class="col-md-4">
                                <md-input-container class="md-block includedsearch">
                                    <label>SN / Ticket ID / Container ID</label>
                                    <input name="TrackSN1" ng-model="TrackSN1" ng-enter="GetSNType1()" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="TrackSN" ng-disabled="!TrackSN1" >
                                        <md-icon md-svg-src="../assets/images/search.svg" ng-click="GetSNType1()"></md-icon>
                                    </md-button>
                                </md-input-container>
                            </div>

                            <div class="col-md-4" style="display: none;">
                                <md-input-container class="md-block includedsearch">
                                    <label>SN / Ticket ID / Container ID</label>
                                    <input name="TrackSN" ng-model="TrackSN" ng-enter="GetSNType()" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="TrackSN" ng-disabled="!TrackSN" >
                                        <md-icon md-svg-src="../assets/images/search.svg" ng-click="GetSNType()"></md-icon>
                                    </md-button>
                                </md-input-container>
                            </div>

                            <div class="col-md-4" style="display: none;">
                                <md-input-container class="md-block includedsearch">
                                    <label>SN</label>
                                    <input name="SerialIDtracking" ng-model="SerialIDtracking" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="SerialIDtracking" ng-disabled="!SerialIDtracking">
                                        <md-icon md-svg-src="../assets/images/search.svg" ng-click="GetAssetDetails()"></md-icon>
                                    </md-button>
                                </md-input-container>
                            </div>
                            <div class="col-md-4" style="display: none;">
                                <md-input-container class="md-block includedsearch">
                                    <label>Inbound Ticket ID</label>
                                    <input name="TicketIDtracking" ng-model="TicketIDtracking" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="TicketIDtracking" ng-disabled="!TicketIDtracking">
                                        <md-icon md-svg-src="../assets/images/search.svg" ng-click="GetTicketDetails()"></md-icon>
                                    </md-button>
                                </md-input-container>
                            </div>
                            <div class="col-md-4" style="display: none;">
                                <md-input-container class="md-block includedsearch">
                                    <label>Inbound Container ID</label>
                                    <input name="ContainerIDtracking" ng-model="ContainerIDtracking" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="ContainerIDtracking" ng-disabled="!ContainerIDtracking">
                                        <md-icon md-svg-src="../assets/images/search.svg" ng-click="GetContainerDetails()"></md-icon>
                                    </md-button>
                                </md-input-container>
                            </div>
                        </div>
                    </form>
                </div>
            </md-card>
            <md-card class="no-margin-h" ng-if="assetssearchresult.length >0">
                <md-card-content>
                    <md-table-container>
                        <table md-table class="table mb-0 multi-tables">
                            <thead md-head>
                                <tr md-row>
                                    <th md-column style="width: 60px; min-width: 60px;">Action</th>
                                    <th md-column>SN</th> 
                                    <th md-column>Container ID</th>
                                    <th md-column>MPN</th>
                                    <th md-column>Disposition</th>
                                    <th md-column>Sanitization Seal ID</th>
                                </tr>
                            </thead>
                            <tbody md-body>
                                <tr md-row ng-repeat="item in assetssearchresult">
                                    <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">
                                        <i ng-click="toggleSidenav();GetserialDetails(item.AssetScanID)" class="material-icons open">open_in_new</i>
                                    </td>
                                    <td md-cell>{{item.SerialNumber}}</td>
                                    <td md-cell>{{item.idPallet}}</td>
                                    <td md-cell>{{item.UniversalModelNumber}}</td>
                                    <td md-cell>{{item.disposition}}</td>
                                    <td md-cell>{{item.sanitization_seal_id}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </md-table-container>
                </md-card-content>
            </md-card>
            <md-card class="no-margin-h" ng-if="ticketsearchresult.length >0">
                <md-card-content>
                    <md-table-container>
                        <table md-table class="table mb-0 multi-tables">
                            <thead md-head>
                                <tr md-row>
                                    <th md-column style="width: 120px; min-width: 120px;">Action</th>
                                    <th md-column style="min-width: 160px;">Ticket ID</th>                                    
                                    <!-- <th md-column>Source</th> -->
                                    <th md-column>Facility</th>
                                </tr>
                            </thead>
                            <tbody md-body>
                                <tr md-row ng-repeat="item in ticketsearchresult">
                                    <td md-cell class="actionicons" style="width: 120px; min-width: 120px;">  
                                        <i class="material-icons add text-warning" ng-click="LoadTracking.showDetails = !LoadTracking.showDetails" ng-show="LoadTracking.showDetails">remove</i>
                                        <i class="material-icons add text-success" ng-click="LoadTracking.showDetails = !LoadTracking.showDetails;GetLoadPalletDetails(item.LoadId)" ng-show="! LoadTracking.showDetails">add</i>                                           
                                        <i ng-click="toggleSidenav2();GetLoadDetails(item.LoadId)" class="material-icons open">open_in_new</i>
                                    </td>
                                    <td md-cell>{{item.LoadId}}</td>
                                    <!-- <td md-cell>{{item.CustomerName}}</td> -->
                                    <td md-cell>{{item.FacilityName}}</td>
                                </tr>
                                <tr ng-show="LoadTracking.showDetails">
                                    <td colspan="4">
                                        <table md-table class="table">
                                            <thead md-head>
                                                <tr md-row>
                                                    <th md-column style="width: 120px; min-width: 120px;">Action</th>
                                                    <th md-column>Container ID</th>
                                                    <th md-column>Source</th>
                                                    <th md-column>Seal ID</th>
                                                    <th md-column>Location</th>
                                                    <th md-column>Received Date</th>                                                                                                                                                   
                                                </tr>
                                            </thead>
                                            <tbody md-body ng-repeat="item in containerpalletdetails">
                                                <tr md-row >
                                                    <td md-cell class="actionicons" style="width: 120px; min-width: 120px;">  
                                                        <i class="material-icons add text-warning" ng-click="item.showDetails = !item.showDetails" ng-show="item.showDetails">remove</i>
                                                        <i class="material-icons add text-success" ng-click="item.showDetails = !item.showDetails;GetPalletAssetDetails(item.idPallet,item)" ng-show="! item.showDetails">add</i>                                           
                                                        <i ng-click="toggleSidenav3();GetLoadPalletDetails1(item.idPallet)" class="material-icons open">open_in_new</i>   
                                                    </td>
                                                    <td md-cell>{{item.idPallet}}</td>
                                                    <td md-cell>{{item.CustomerName}}</td>
                                                    <td md-cell>{{item.SealNo1}}</td>
                                                    <td md-cell>{{item.LocationName}}</td>
                                                    <td md-cell>{{item.ReceivedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                                                </tr>

                                                <tr ng-show="item.showDetails">
                                                    <td colspan="7">
                                                        <table md-table class="table mb-0 multi-tables" style="margin-left: 30px;">
                                                            <thead md-head>
                                                                <tr md-row>
                                                                    <th md-column style="width: 60px; min-width: 60px;">Action</th>
                                                                    <th md-column>SN</th> 
                                                                    <th md-column>Container ID</th>
                                                                    <th md-column>MPN</th>
                                                                    <th md-column>Disposition</th>                                                                                   
                                                                </tr>
                                                            </thead>
                                                            <tbody md-body>
                                                                <tr md-row ng-repeat="asset in item.Assets">
                                                                    <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">
                                                                        <i ng-click="toggleSidenav();GetserialDetails(asset.AssetScanID)" class="material-icons open">open_in_new</i>
                                                                    </td>
                                                                    <td md-cell>{{asset.SerialNumber}}</td>
                                                                    <td md-cell>{{asset.idPallet}}</td>
                                                                    <td md-cell>{{asset.UniversalModelNumber}}</td>
                                                                    <td md-cell>{{asset.disposition}}</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </md-table-container>
                </md-card-content>
            </md-card>
            <md-card class="no-margin-h" ng-if="containerpalletdetails1.length >0">
                <md-card-content>
                    <md-table-container>
                        <table md-table class="table">
                            <thead md-head>
                                <tr md-row>
                                    <th md-column style="width: 120px; min-width: 120px;">Action</th>
                                    <th md-column>Container ID</th>
                                    <th md-column>Source</th>
                                    <th md-column>Ticket ID</th>
                                    <th md-column>Seal ID</th>
                                    <th md-column>Location</th>
                                    <th md-column>Received Date</th>                                                                                                                                                   
                                </tr>
                            </thead>
                            <tbody md-body ng-repeat="item in containerpalletdetails1">
                                <tr md-row >
                                    <td md-cell class="actionicons" style="width: 120px; min-width: 120px;">  
                                        <i class="material-icons add text-warning" ng-click="item.showDetails = !item.showDetails" ng-show="item.showDetails">remove</i>
                                        <i class="material-icons add text-success" ng-click="item.showDetails = !item.showDetails;GetPalletAssetDetails(item.idPallet,item)" ng-show="! item.showDetails">add</i>                                           
                                        <i ng-click="toggleSidenav3();GetLoadPalletDetails1(item.idPallet)" class="material-icons open">open_in_new</i>
                                    </td>
                                    <td md-cell>{{item.idPallet}}</td>
                                    <td md-cell>{{item.CustomerName}}</td>
                                    <td md-cell>{{item.LoadId}}</td>
                                    <td md-cell>{{item.SealNo1}}</td>
                                    <td md-cell>{{item.LocationName}}</td>
                                    <td md-cell>{{item.ReceivedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                                </tr>

                                <tr ng-show="item.showDetails">
                                    <td colspan="7">
                                        <table md-table class="table mb-0 multi-tables" style="margin-left: 30px;">
                                            <thead md-head>
                                                <tr md-row>
                                                    <th md-column style="width: 60px; min-width: 60px;">Action</th>
                                                    <th md-column>SN</th> 
                                                    <th md-column>Container ID</th>
                                                    <th md-column>MPN</th>
                                                    <th md-column>Disposition</th>                                                                                                                          
                                                </tr>
                                            </thead>
                                            <tbody md-body>
                                                <tr md-row ng-repeat="asset in item.Assets">
                                                    <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">
                                                        <i ng-click="toggleSidenav();GetserialDetails(asset.AssetScanID)" class="material-icons open">open_in_new</i>
                                                    </td>
                                                    <td md-cell>{{asset.SerialNumber}}</td>
                                                    <td md-cell>{{asset.idPallet}}</td>
                                                    <td md-cell>{{asset.UniversalModelNumber}}</td>
                                                    <td md-cell>{{asset.disposition}}</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </md-table-container>
                </md-card-content>
            </md-card>



            <md-card class="no-margin-h" ng-if="ContainerDetails.length >0">
                <md-card-content>
                    <md-table-container>
                        <table md-table class="table">
                            <thead md-head>
                                <tr md-row>
                                    <th md-column style="width: 120px; min-width: 120px;">Action</th>
                                    <th md-column>Container ID</th>
                                    <th md-column>Source</th>
                                    <th md-column>Ticket ID</th>
                                    <th md-column>Seal ID</th>
                                    <th md-column>Location</th>
                                    <th md-column>Status</th>
                                    <th md-column>Received Date</th>                                                                                                                                                   
                                </tr>
                            </thead>
                            <tbody md-body ng-repeat="item in ContainerDetails">
                                <tr md-row >
                                    <td md-cell class="actionicons" style="width: 120px; min-width: 120px;">  
                                        <i class="material-icons add text-warning" ng-click="item.showDetails = !item.showDetails" ng-show="item.showDetails">remove</i>
                                        <i class="material-icons add text-success" ng-click="item.showDetails = !item.showDetails;GetContainerRecovery(item.idPallet,item)" ng-show="! item.showDetails">add</i>                                           
                                        <i ng-click="toggleSidenav3();GetLoadPalletDetails1(item.idPallet)" class="material-icons open">open_in_new</i>
                                    </td>
                                    <td md-cell>{{item.idPallet}}</td>
                                    <td md-cell>{{item.CustomerName}}</td>
                                    <td md-cell>{{item.LoadId}}</td>
                                    <td md-cell>{{item.SealNo1}}</td>
                                    <td md-cell>{{item.LocationName}}</td>
                                    <td md-cell>{{item.StatusValue}}</td>
                                    <td md-cell>{{item.ReceivedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                                </tr>

                                <tr ng-show="item.showDetails">
                                    <td colspan="7">
                                        <table md-table class="table mb-0 multi-tables" style="margin-left: 30px;">
                                            <thead md-head>
                                                <tr md-row>
                                                    <th md-column style="width: 60px; min-width: 60px;">Action</th>
                                                    <th md-column>SN</th> 
                                                    <th md-column>Container ID</th>
                                                    <th md-column>MPN</th>
                                                    <th md-column>Disposition</th>
                                                    <th md-column>Recovery Type</th>                                                                                                                          
                                                </tr>
                                            </thead>
                                            <tbody md-body ng-repeat="asset in item.Assets">
                                                <tr md-row>
                                                    <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">

                                                        <i class="material-icons add text-warning" ng-click="asset.showDetails = !asset.showDetails" ng-show="asset.showDetails">remove</i>
                                                        <i class="material-icons add text-success" ng-click="asset.showDetails = !asset.showDetails;GetNextLevelRecovery(asset.SerialNumber,asset)" ng-show="! asset.showDetails">add</i>                                           
                                                        <i ng-show= "asset.AssetScanID" ng-click="toggleSidenav();GetserialDetails(asset.AssetScanID)" class="material-icons open">open_in_new</i>
                                                        <i ng-show="asset.ServerID" ng-click="toggleSidenav11();GetServerLifeCycle(asset)" class="material-icons open">open_in_new</i>

                                                    </td>
                                                    <td md-cell>{{asset.SerialNumber}}</td>
                                                    <td md-cell>{{asset.idPallet}}</td>
                                                    <td md-cell>{{asset.UniversalModelNumber}}</td>
                                                    <td md-cell>{{asset.disposition}}</td>
                                                    <td md-cell>{{asset.Recoverytype}}</td>
                                                </tr>
                                                <tr ng-show="asset.showDetails">
                                                    <td colspan="6">
                                                        <div ng-show="asset.NewLevel.length == 0">No Serials Available</div>
                                                        <table md-table class="table mb-0 multi-tables" style="margin-left: 30px;" ng-show="asset.NewLevel.length > 0">
                                                            <thead md-head>
                                                                <tr md-row>
                                                                    <th md-column>Action</th>
                                                                    <th md-column>SN</th> 
                                                                    <th md-column>Part Type</th>
                                                                    <th md-column>MPN</th>
                                                                    <th md-column>Disposition</th>
                                                                    <th md-column>Recovery Type</th>                                                                                                                          
                                                                </tr>
                                                            </thead>
                                                            <tbody md-body ng-repeat="asset1 in asset.NewLevel">
                                                                <tr md-row>
                                                                    <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">
                
                                                                        <i class="material-icons add text-warning" ng-click="asset1.showDetails = !asset1.showDetails" ng-show="asset1.showDetails">remove</i>
                                                                        <i class="material-icons add text-success" ng-click="asset1.showDetails = !asset1.showDetails;GetNextLevelRecovery(asset1.SerialNumber,asset1)" ng-show="! asset1.showDetails">add</i>                                           
                                                                        <i ng-show="asset1.AssetScanID" ng-click="toggleSidenav();GetserialDetails(asset1.AssetScanID)" class="material-icons open">open_in_new</i>
                                                                        <i ng-show="asset1.MediaID" ng-click="toggleSidenav10();GetMediaLifeCycle(asset1)" class="material-icons open">open_in_new</i>
                
                                                                    </td>
                                                                    <td md-cell>{{asset1.SerialNumber}}</td>
                                                                    <td md-cell>{{asset1.parttype}}</td>
                                                                    <td md-cell>{{asset1.UniversalModelNumber}}</td>
                                                                    <td md-cell>{{asset1.disposition}}</td>
                                                                    <td md-cell>{{asset1.Recoverytype}}</td>
                                                                </tr>
                                                                <tr ng-show="asset1.showDetails">
                                                                    <td colspan="6">
                                                                        <div ng-show="asset1.NewLevel.length == 0">No Serials Available</div>
                                                                        <table md-table class="table mb-0 multi-tables" style="margin-left: 30px;" ng-show="asset1.NewLevel.length > 0">
                                                                            <thead md-head>
                                                                                <tr md-row>
                                                                                    <th md-column>Action</th>
                                                                                    <th md-column>SN</th> 
                                                                                    <th md-column>Part Type</th>
                                                                                    <th md-column>MPN</th>
                                                                                    <th md-column>Disposition</th>
                                                                                    <th md-column>Recovery Type</th>                                                                                                                          
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody md-body ng-repeat="asset2 in asset1.NewLevel">
                                                                                <tr md-row>
                                                                                    <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">
                                
                                                                                        <!-- <i class="material-icons add text-warning" ng-click="asset2.showDetails = !asset2.showDetails" ng-show="asset2.showDetails">remove</i>
                                                                                        <i class="material-icons add text-success" ng-click="asset2.showDetails = !asset2.showDetails;GetNextLevelRecovery(asset2.SerialNumber,asset2)" ng-show="! asset2.showDetails">add</i>                                            -->
                                                                                        <i ng-show="asset2.AssetScanID" ng-click="toggleSidenav();GetserialDetails(asset2.AssetScanID)" class="material-icons open">open_in_new</i>
                                                                                        <i ng-show="asset2.MediaID" ng-click="toggleSidenav10();GetMediaLifeCycle(asset2)" class="material-icons open">open_in_new</i>
                                
                                                                                    </td>
                                                                                    <td md-cell>{{asset2.SerialNumber}}</td>
                                                                                    <td md-cell>{{asset2.parttype}}</td>
                                                                                    <td md-cell>{{asset2.UniversalModelNumber}}</td>
                                                                                    <td md-cell>{{asset2.disposition}}</td>
                                                                                    <td md-cell>{{asset2.Recoverytype}}</td>
                                                                                </tr>
                                                                                <tr ng-show="asset2.showDetails">
                                                                                    <td colspan="6">
                                                                                        
                                                                                        
                                                                                        
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>

                                                        
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </md-table-container>
                </md-card-content>
            </md-card>




            <md-card class="no-margin-h" ng-if="SerialItem.length > 0">
                <md-card-content>
                    <md-table-container>
                        
                        <table md-table class="table">
                            <thead md-head>
                                <tr md-row>
                                    <th md-column style="width: 60px; min-width: 60px;">Action</th>
                                    <th md-column>SN</th> 
                                    <th md-column>Container ID</th>
                                    <th md-column>MPN</th>
                                    <th md-column>Disposition</th>
                                    <th md-column>Recovery Type</th>
                                    <th md-column>Sanitization Seal ID</th>                                                                                                                          
                                </tr>
                            </thead>
                            <tbody md-body ng-repeat="asset in SerialItem">
                                <tr md-row>
                                    <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">

                                        <i class="material-icons add text-warning" ng-click="asset.showDetails = !asset.showDetails" ng-show="asset.showDetails">remove</i>
                                        <i class="material-icons add text-success" ng-click="asset.showDetails = !asset.showDetails;GetNextLevelRecovery(asset.SerialNumber,asset)" ng-show="! asset.showDetails">add</i>                                           
                                        <i ng-show= "asset.AssetScanID" ng-click="toggleSidenav();GetserialDetails(asset.AssetScanID)" class="material-icons open">open_in_new</i>
                                        <i ng-show="asset.ServerID" ng-click="toggleSidenav11();GetServerLifeCycle(asset)" class="material-icons open">open_in_new</i>

                                    </td>
                                    <td md-cell>{{asset.SerialNumber}}</td>
                                    <td md-cell>{{asset.idPallet}}</td>
                                    <td md-cell>{{asset.UniversalModelNumber}}</td>
                                    <td md-cell>{{asset.disposition}}</td>
                                    <td md-cell>{{asset.Recoverytype}}</td>
                                    <td md-cell>{{asset.sanitization_seal_id}}</td>
                                </tr>
                                <tr ng-show="asset.showDetails">
                                    <td colspan="7">
                                        <div ng-show="asset.NewLevel.length == 0">No Serials Available</div>
                                        <table md-table class="table mb-0 multi-tables" style="margin-left: 30px;" ng-show="asset.NewLevel.length > 0">
                                            <thead md-head>
                                                <tr md-row>
                                                    <th md-column>Action</th>
                                                    <th md-column>SN</th> 
                                                    <th md-column>Part Type</th>
                                                    <th md-column>MPN</th>
                                                    <th md-column>Disposition</th>
                                                    <th md-column>Recovery Type</th>                                                                                                                          
                                                </tr>
                                            </thead>
                                            <tbody md-body ng-repeat="asset1 in asset.NewLevel">
                                                <tr md-row>
                                                    <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">

                                                        <i class="material-icons add text-warning" ng-click="asset1.showDetails = !asset1.showDetails" ng-show="asset1.showDetails">remove</i>
                                                        <i class="material-icons add text-success" ng-click="asset1.showDetails = !asset1.showDetails;GetNextLevelRecovery(asset1.SerialNumber,asset1)" ng-show="! asset1.showDetails">add</i>                                           
                                                        <i ng-show="asset1.AssetScanID" ng-click="toggleSidenav();GetserialDetails(asset1.AssetScanID)" class="material-icons open">open_in_new</i>
                                                        <i ng-show="asset1.MediaID" ng-click="toggleSidenav10();GetMediaLifeCycle(asset1)" class="material-icons open">open_in_new</i>

                                                    </td>
                                                    <td md-cell>{{asset1.SerialNumber}}</td>
                                                    <td md-cell>{{asset1.parttype}}</td>
                                                    <td md-cell>{{asset1.UniversalModelNumber}}</td>
                                                    <td md-cell>{{asset1.disposition}}</td>
                                                    <td md-cell>{{asset1.Recoverytype}}</td>
                                                </tr>
                                                <tr ng-show="asset1.showDetails">
                                                    <td colspan="6">
                                                        <div ng-show="asset1.NewLevel.length == 0">No Serials Available</div>
                                                        <table md-table class="table mb-0 multi-tables" style="margin-left: 30px;" ng-show="asset1.NewLevel.length > 0">
                                                            <thead md-head>
                                                                <tr md-row>
                                                                    <th md-column>Action</th>
                                                                    <th md-column>SN</th> 
                                                                    <th md-column>Part Type</th>
                                                                    <th md-column>MPN</th>
                                                                    <th md-column>Disposition</th>
                                                                    <th md-column>Recovery Type</th>                                                                                                                          
                                                                </tr>
                                                            </thead>
                                                            <tbody md-body ng-repeat="asset2 in asset1.NewLevel">
                                                                <tr md-row>
                                                                    <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">
                
                                                                        <!-- <i class="material-icons add text-warning" ng-click="asset2.showDetails = !asset2.showDetails" ng-show="asset2.showDetails">remove</i>
                                                                        <i class="material-icons add text-success" ng-click="asset2.showDetails = !asset2.showDetails;GetNextLevelRecovery(asset2.SerialNumber,asset2)" ng-show="! asset2.showDetails">add</i>                                            -->
                                                                        <i ng-show="asset2.AssetScanID" ng-click="toggleSidenav();GetserialDetails(asset2.AssetScanID)" class="material-icons open">open_in_new</i>
                                                                        <i ng-show="asset2.MediaID" ng-click="toggleSidenav10();GetMediaLifeCycle(asset2)" class="material-icons open">open_in_new</i>
                
                                                                    </td>
                                                                    <td md-cell>{{asset2.SerialNumber}}</td>
                                                                    <td md-cell>{{asset2.parttype}}</td>
                                                                    <td md-cell>{{asset2.UniversalModelNumber}}</td>
                                                                    <td md-cell>{{asset2.disposition}}</td>
                                                                    <td md-cell>{{asset2.Recoverytype}}</td>
                                                                </tr>
                                                                <tr ng-show="asset2.showDetails">
                                                                    <td colspan="6">
                                                                        
                                                                        
                                                                        
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>

                                        
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                    </md-table-container>
                </md-card-content>
            </md-card>



            <md-card class="no-margin-h" ng-if="LoadItem.length > 0">
                <md-card-content>
                    <md-table-container>
                        
                        <table md-table class="table">
                            <thead md-head>
                                <tr md-row>
                                    <th md-column >Action</th>
                                    <th md-column>Ticket ID</th> 
                                    <th md-column>Facility</th>                                                                                                                                                          
                                </tr>
                            </thead>
                            <tbody md-body ng-repeat="asset in LoadItem">
                                <tr>
                                    <td>
                                        <i class="material-icons add text-warning" ng-click="asset.showDetails = !asset.showDetails" ng-show="asset.showDetails">remove</i>
                                        <i class="material-icons add text-success" ng-click="asset.showDetails = !asset.showDetails;GetLoadPalletDetails(asset.LoadId,asset)" ng-show="! asset.showDetails">add</i>                                           
                                        <i ng-click="toggleSidenav2();GetLoadDetails(asset.LoadId)" class="material-icons open">open_in_new</i>
                                    </td>
                                    <td>{{asset.LoadId}}</td>
                                    <td>{{asset.FacilityName}}</td>
                                </tr>

                                <tr ng-show="asset.showDetails">
                                    <td colspan="3">

                                        <md-card  ng-if="asset.ContainerDetails.length >0">
                                            <md-card-content>
                                                <md-table-container>
                                                    <table md-table class="table">
                                                        <thead md-head>
                                                            <tr md-row>
                                                                <th md-column style="width: 120px; min-width: 120px;">Action</th>
                                                                <th md-column>Container ID</th>
                                                                <th md-column>Source</th>
                                                                <th md-column>Ticket ID</th>
                                                                <th md-column>Seal ID</th>
                                                                <th md-column>Location</th>
                                                                <th md-column>Received Date</th>                                                                                                                                                   
                                                            </tr>
                                                        </thead>
                                                        <tbody md-body ng-repeat="item in asset.ContainerDetails">
                                                            <tr md-row >
                                                                <td md-cell class="actionicons" style="width: 120px; min-width: 120px;">  
                                                                    <i class="material-icons add text-warning" ng-click="item.showDetails = !item.showDetails" ng-show="item.showDetails">remove</i>
                                                                    <i class="material-icons add text-success" ng-click="item.showDetails = !item.showDetails;GetContainerRecovery(item.idPallet,item)" ng-show="! item.showDetails">add</i>                                           
                                                                    <i ng-click="toggleSidenav3();GetLoadPalletDetails1(item.idPallet)" class="material-icons open">open_in_new</i>
                                                                </td>
                                                                <td md-cell>{{item.idPallet}}</td>
                                                                <td md-cell>{{item.CustomerName}}</td>
                                                                <td md-cell>{{item.LoadId}}</td>
                                                                <td md-cell>{{item.SealNo1}}</td>
                                                                <td md-cell>{{item.LocationName}}</td>
                                                                <td md-cell>{{item.ReceivedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                                                            </tr>
                            
                                                            <tr ng-show="item.showDetails">
                                                                <td colspan="7">
                                                                    <table md-table class="table mb-0 multi-tables" style="margin-left: 30px;">
                                                                        <thead md-head>
                                                                            <tr md-row>
                                                                                <th md-column style="width: 60px; min-width: 60px;">Action</th>
                                                                                <th md-column>SN</th> 
                                                                                <th md-column>Container ID</th>
                                                                                <th md-column>MPN</th>
                                                                                <th md-column>Disposition</th>
                                                                                <th md-column>Recovery Type</th>                                                                                                                          
                                                                            </tr>
                                                                        </thead>
                                                                        <tbody md-body ng-repeat="asset in item.Assets">
                                                                            <tr md-row>
                                                                                <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">
                            
                                                                                    <i class="material-icons add text-warning" ng-click="asset.showDetails = !asset.showDetails" ng-show="asset.showDetails">remove</i>
                                                                                    <i class="material-icons add text-success" ng-click="asset.showDetails = !asset.showDetails;GetNextLevelRecovery(asset.SerialNumber,asset)" ng-show="! asset.showDetails">add</i>                                           
                                                                                    <i ng-show= "asset.AssetScanID" ng-click="toggleSidenav();GetserialDetails(asset.AssetScanID)" class="material-icons open">open_in_new</i>
                                                                                    <i ng-show="asset.ServerID" ng-click="toggleSidenav11();GetServerLifeCycle(asset)" class="material-icons open">open_in_new</i>
                            
                                                                                </td>
                                                                                <td md-cell>{{asset.SerialNumber}}</td>
                                                                                <td md-cell>{{asset.idPallet}}</td>
                                                                                <td md-cell>{{asset.UniversalModelNumber}}</td>
                                                                                <td md-cell>{{asset.disposition}}</td>
                                                                                <td md-cell>{{asset.Recoverytype}}</td>
                                                                            </tr>
                                                                            <tr ng-show="asset.showDetails">
                                                                                <td colspan="6">
                                                                                    <div ng-show="asset.NewLevel.length == 0">No Serials Available</div>
                                                                                    <table md-table class="table mb-0 multi-tables" style="margin-left: 30px;" ng-show="asset.NewLevel.length > 0">
                                                                                        <thead md-head>
                                                                                            <tr md-row>
                                                                                                <th md-column>Action</th>
                                                                                                <th md-column>SN</th> 
                                                                                                <th md-column>Part Type</th>
                                                                                                <th md-column>MPN</th>
                                                                                                <th md-column>Disposition</th>
                                                                                                <th md-column>Recovery Type</th>                                                                                                                          
                                                                                            </tr>
                                                                                        </thead>
                                                                                        <tbody md-body ng-repeat="asset1 in asset.NewLevel">
                                                                                            <tr md-row>
                                                                                                <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">
                                            
                                                                                                    <i class="material-icons add text-warning" ng-click="asset1.showDetails = !asset1.showDetails" ng-show="asset1.showDetails">remove</i>
                                                                                                    <i class="material-icons add text-success" ng-click="asset1.showDetails = !asset1.showDetails;GetNextLevelRecovery(asset1.SerialNumber,asset1)" ng-show="! asset1.showDetails">add</i>                                           
                                                                                                    <i ng-show="asset1.AssetScanID" ng-click="toggleSidenav();GetserialDetails(asset1.AssetScanID)" class="material-icons open">open_in_new</i>
                                                                                                    <i ng-show="asset1.MediaID" ng-click="toggleSidenav10();GetMediaLifeCycle(asset1)" class="material-icons open">open_in_new</i>
                                            
                                                                                                </td>
                                                                                                <td md-cell>{{asset1.SerialNumber}}</td>
                                                                                                <td md-cell>{{asset1.parttype}}</td>
                                                                                                <td md-cell>{{asset1.UniversalModelNumber}}</td>
                                                                                                <td md-cell>{{asset1.disposition}}</td>
                                                                                                <td md-cell>{{asset1.Recoverytype}}</td>
                                                                                            </tr>
                                                                                            <tr ng-show="asset1.showDetails">
                                                                                                <td colspan="6">
                                                                                                    <div ng-show="asset1.NewLevel.length == 0">No Serials Available</div>
                                                                                                    <table md-table class="table mb-0 multi-tables" style="margin-left: 30px;" ng-show="asset1.NewLevel.length > 0">
                                                                                                        <thead md-head>
                                                                                                            <tr md-row>
                                                                                                                <th md-column>Action</th>
                                                                                                                <th md-column>SN</th> 
                                                                                                                <th md-column>Part Type</th>
                                                                                                                <th md-column>MPN</th>
                                                                                                                <th md-column>Disposition</th>
                                                                                                                <th md-column>Recovery Type</th>                                                                                                                          
                                                                                                            </tr>
                                                                                                        </thead>
                                                                                                        <tbody md-body ng-repeat="asset2 in asset1.NewLevel">
                                                                                                            <tr md-row>
                                                                                                                <td md-cell class="actionicons dialog-demo-content" style="width: 60px;">
                                                            
                                                                                                                    <!-- <i class="material-icons add text-warning" ng-click="asset2.showDetails = !asset2.showDetails" ng-show="asset2.showDetails">remove</i>
                                                                                                                    <i class="material-icons add text-success" ng-click="asset2.showDetails = !asset2.showDetails;GetNextLevelRecovery(asset2.SerialNumber,asset2)" ng-show="! asset2.showDetails">add</i>                                            -->
                                                                                                                    <i ng-show="asset2.AssetScanID" ng-click="toggleSidenav();GetserialDetails(asset2.AssetScanID)" class="material-icons open">open_in_new</i>
                                                                                                                    <i ng-show="asset2.MediaID" ng-click="toggleSidenav10();GetMediaLifeCycle(asset2)" class="material-icons open">open_in_new</i>
                                                            
                                                                                                                </td>
                                                                                                                <td md-cell>{{asset2.SerialNumber}}</td>
                                                                                                                <td md-cell>{{asset2.parttype}}</td>
                                                                                                                <td md-cell>{{asset2.UniversalModelNumber}}</td>
                                                                                                                <td md-cell>{{asset2.disposition}}</td>
                                                                                                                <td md-cell>{{asset2.Recoverytype}}</td>
                                                                                                            </tr>
                                                                                                            <tr ng-show="asset2.showDetails">
                                                                                                                <td colspan="6">
                                                                                                                    
                                                                                                                    
                                                                                                                    
                                                                                                                </td>
                                                                                                            </tr>
                                                                                                        </tbody>
                                                                                                    </table>
                                                                                                    
                                                                                                </td>
                                                                                            </tr>
                                                                                        </tbody>
                                                                                    </table>
                            
                                                                                    
                                                                                </td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </md-table-container>
                                            </md-card-content>
                                        </md-card>

                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </md-table-container>
                </md-card-content>
            </md-card>


        </article>        
    </div>
</div>