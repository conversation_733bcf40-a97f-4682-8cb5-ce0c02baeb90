<?php
session_start();
$id = $_GET['id'];
// Include the main TCPDF library (search for installation path).
require_once('tcpdf_include.php');
// create new PDF document
//$pdf = new TCPDF('L', 'mm', array('120','110'), true, 'UTF-8', false);

//$pdf = new TCPDF('P', 'mm', array('60','127'), true, 'UTF-8', false);
$pdf = new TCPDF('P', 'mm', array('90','70'), true, 'UTF-8', false);
//$pdf = new TCPDF('P', 'mm', array('101.6','152.4'), true, 'UTF-8', false);
// set document information
$pdf->SetCreator(PDF_CREATOR);
$pdf->SetAuthor('Eviridis');
$pdf->SetTitle('Shipment Container Label');
$pdf->SetSubject('Shipment Container Label');
$pdf->SetKeywords('TCPDF, PDF, example, test, guide');
// remove default header/footer
$pdf->setPrintHeader(false);
$pdf->setPrintFooter(false);
// set default monospaced font
$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
// set margins
$pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
$pdf->setTopMargin(0);
$pdf->SetRightMargin(0);
$pdf->setHeaderMargin(0);
$pdf->SetFooterMargin(0);
// set auto page breaks
$pdf->SetAutoPageBreak(TRUE, 0);
// set image scale factor
$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
// set some language-dependent strings (optional)
if (@file_exists(dirname(__FILE__).'/lang/eng.php')) {
    require_once(dirname(__FILE__).'/lang/eng.php');
    $pdf->setLanguageArray($l);
}
// ---------------------------------------------------------
// set font
//$pdf->SetFont('helvetica', '', 11);
//$pdf->SetFont("helvetica", "B", 13);
$pdf->SetFont("helvetica", "B", 10);
include_once("../../../connection.php");
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
include_once("../../config.php");
$dateformat = DATEFORMAT;


/*$sql = "select sc.*,d.disposition,v.VendorName,s.ShippingID,scs.UniversalModelNumber,scs.part_type from shipping_containers sc
left join shipping s on s.ShippingID = sc.ShippingID
left join disposition d on sc.disposition_id = d.disposition_id 
left join vendor v on s.VendorID = v.VendorID 
left join shipping_container_serials scs on sc.ShippingContainerID = scs.ShippingContainerID
where sc.ShippingContainerID = '".mysqli_real_escape_string($connectionlink,$id)."'";*/

$sql = "select sc.*,d.disposition,v.VendorName,s.ShippingID,l.LocationName,g.GroupName,g.GroupName as `group` from shipping_containers sc
left join shipping s on s.ShippingID = sc.ShippingID
left join disposition d on sc.disposition_id = d.disposition_id 
left join vendor v on s.VendorID = v.VendorID 
left join location l on sc.LocationID = l.LocationID  
LEFT JOIN location_group g on l.GroupID = g.GroupID 
where sc.ShippingContainerID = '".mysqli_real_escape_string($connectionlink,$id)."'";

$query = mysqli_query($connectionlink,$sql);
if(mysqli_error($connectionlink)) {
	echo mysqli_error($connectionlink);
}
$row = mysqli_fetch_assoc($query);

$sqlserialcount = "Select count(*) as serialcount from shipping_container_serials where ShippingContainerID = '".$row['ShippingContainerID']."'";
$queryserialcount = mysqli_query($connectionlink,$sqlserialcount);
if(mysqli_error($connectionlink)) {
	echo mysqli_error($connectionlink);
}
$rowserialcount = mysqli_fetch_assoc($queryserialcount);

//$sqlshipcontainerserial = "Select SerialNumber from shipping_container_serials where ShippingContainerID = '".$row['ShippingContainerID']."'";
$sqlshipcontainerserial = "select distinct(part_type) as part_type FROM shipping_container_serials where ShippingContainerID = '".$row['ShippingContainerID']."'";
$queryshipcontainerserial = mysqli_query($connectionlink,$sqlshipcontainerserial);

$part_types = '';
$i = 0;
while($row_pallet = mysqli_fetch_assoc($queryshipcontainerserial)) {
	if($i ==0 ) {
		$part_types = $part_types . $row_pallet['part_type'];
	} else {
		$part_types = $part_types . ','. $row_pallet['part_type'];
	}
	$i++;
}

//$parttype = $part_types ;

if($i == 1) {
	$parttype = $part_types;
} else {
	$parttype = '';
}
$parttype_1 = "Part Type :".$parttype;


$sqlshipcontainerserial = "select distinct(UniversalModelNumber) as UniversalModelNumber FROM shipping_container_serials where ShippingContainerID = '".$row['ShippingContainerID']."'";
$queryshipcontainerserial = mysqli_query($connectionlink,$sqlshipcontainerserial);

$mpns = '';
$i = 0;
while($row_pallet = mysqli_fetch_assoc($queryshipcontainerserial)) {
	if($i ==0 ) {
		$mpns = $mpns . $row_pallet['UniversalModelNumber'];
	} else {
		$mpns = $mpns . ','. $row_pallet['UniversalModelNumber'];
	}
	$i++;
}

if($i == 1) {
	$mpn = $mpns;
} else {
	$mpn = '';
}
$mpn_1 = "MPN :".$mpn;


$sqlshipcontainerserial = "select distinct(COO) as COO FROM shipping_container_serials where ShippingContainerID = '".$row['ShippingContainerID']."'";
$queryshipcontainerserial = mysqli_query($connectionlink,$sqlshipcontainerserial);

$coos = '';
$j = 0;
while($row_pallet = mysqli_fetch_assoc($queryshipcontainerserial)) {
	if($j ==0 ) {
		$coos = $coos . $row_pallet['COO'];
	} else {
		$coos = $coos . ','. $row_pallet['COO'];
	}
	$j++;
}

if($j == 1) {
	$coo = $coos;
} else {
	$coo = '';
}
$coo_1 = "Country of Origin :".$coo;


if($i == 1) {
	$query11 = "select coo_id,ext_mpn_id from catlog_creation where mpn_id = '".mysqli_real_escape_string($connectionlink,$mpns)."' and FacilityID = '".$_SESSION['user']['FacilityID']."' ";
	$q11 = mysqli_query($connectionlink,$query11);
	if(mysqli_affected_rows($connectionlink) > 0) {
		$row11 = mysqli_fetch_assoc($q11);
		//$coo = $row11['coo_id'];
		$ext_mpn = $row11['ext_mpn_id'];
	} else {
		//$coo = 'Unknown';
		$ext_mpn = 'Unknown';
	}
} else {
	//$coo = 'Unknown';
	//$coo = '';
	$ext_mpn = '';
}

$ShippingContainerID = $row['ShippingContainerID'];
$ShippingContainerID_1 = "Container ID :".$ShippingContainerID;

$LocationName = $row['LocationName'];
$LocationName_1 = "Location :".$LocationName;

$ContainerWeight = $row['ContainerWeight'];
$ContainerWeight_1 = "Weight :".$ContainerWeight;

$Seal = $row['SealID'];
$Seal_1 = "Seal :".$Seal;

$RemovalCode = $row['RemovalCode'];
$RemovalCode_1 = "RemovalCode :".$RemovalCode;

$pdf->AddPage();
		if(1) {			
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,$ShippingContainerID_1,0,1,'L',0,0,true,'','T');		
			$barcode = ($pdf->write1DBarcode($ShippingContainerID, 'C128', '15', '', 28, 12, '', $style, 'C'));	
		}
		if(1) {		
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');	
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,$ContainerWeight_1,0,1,'L',0,0,true,'','T');		
			$barcode = ($pdf->write1DBarcode($ContainerWeight, 'C128', '15', '', 28, 12, '', $style, 'C'));	
		}
		if(1) {		
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');	
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,$LocationName_1,0,1,'L',0,0,true,'','T');		
			$barcode = ($pdf->write1DBarcode($LocationName, 'C128', '15', '', 28, 12, '', $style, 'C'));	
		}		

		if(1) {		
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');	
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,$Seal_1,0,1,'L',0,0,true,'','T');		
			$barcode = ($pdf->write1DBarcode($Seal, 'C128', '15', '', 28, 12, '', $style, 'C'));	
		}

		if(1) {	
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');	
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');		
		 	$pdf->setX(3);
		 	$pdf->Cell(0 , 0,$RemovalCode_1,0,1,'L',0,0,true,'','T');		
		 	//$barcode = ($pdf->write1DBarcode($RemovalCode, 'C128', '15', '', 28, 12, '', $style, 'C'));	
		}

// ---------------------------------------------------------
//Close and output PDF document
$pdf->Output('ShipmentOutboundContainerLabel.pdf', 'I');
//============================================================+
// END OF FILE
//============================================================+