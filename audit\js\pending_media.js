(function () {
    'use strict';

    angular.module('app').controller("pending_media", function ($scope,$http,$filter,$upload,$rootScope,$mdToast,$mdDialog,$stateParams,$window) {    
        
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=Pending Media',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });
        
        $rootScope.$broadcast('preloader:active');        
        $scope.busy = false;        
        $scope.Assets = [];

        //Start Pagination Logic
        $scope.itemsPerPage = 20;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];
        $scope.range = function() {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function() {
            $scope.currentPage =  $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage);
        };
        $scope.setPage = function(n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {            
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({                
                url: host+'audit/includes/pending_media_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetPendingMedia&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {

                        $scope.Assets = data.Result;
                        if(data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    alert(data.Result);
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.$watch("currentPage", function(newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for(var i=0;i<multiarray.length;i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'audit/includes/pending_media_submit.php',
                dataType: 'json',
                type: 'post',                
                data: 'ajax=GetPendingMedia&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.Assets = data.Result;
                        if(data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter = function () {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };
        //End Pagination Logic

        $scope.PendingMediaPannelxls = function () {
            //alert("1");
            jQuery.ajax({
                url: host+'audit/includes/pending_media_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GeneratePendingMediaPannelxls',
                success: function(data) {
                    if(data.Success) {
                        //alert("2");
                        window.location="templates/PendingMediaPannelxls.php";
                    } else {
                        //alert("4");
                        $mdToast.show (
                            $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    //alert(data.Result);
                    //alert("3");
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };        


        function QuarantineToActiveTPVRController($scope,$mdDialog,CurrentPallet,$mdToast,$window) {
            $scope.CurrentPallet = CurrentPallet;
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails);
            };


            $scope.GetCurrentTime = function(object,item) {
                if (!object || typeof object !== 'object') {
                  console.log('Invalid scope object provided');
                }
                jQuery.ajax({
                    url: host+'recovery/includes/recovery_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetCurrentTime',
                    success: function(data){
                        if(data.Success) {
                            object[item] = data.Result;
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content('Invalid')
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            object[item] = '';
                        }
                        //console.log('Scan Object = '+JSON.stringify(object));
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        initSessionTime(); $scope.$apply();
                    }
                });
            };

            $scope.GetCurrentTime($scope.CurrentPallet,'serial_scan_time');

            $scope.NextBins = [];
            if($scope.NextBins.length == 0) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/pending_media_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetAllNextBins',
                    success: function(data) {
                        if(data.Success) {
                            $scope.NextBins = data.Result;
                            console.log(data.Result);
                        } else {                            
                            $scope.NextBins = [];                        
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        //alert(data.Result);
                        //alert("3");
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }

            $scope.ValidateNextBinID = function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/pending_media_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ValidateNextBinID&BinID='+$scope.confirmDetails.nextbinid,
                    success: function(data) {
                        if(data.Success) {
                            $scope.confirmDetails.NewCustomPalletID = data.CustomPalletID;     
                            $window.document.getElementById("AuditController").focus();                       
                        } else {                            
                            $scope.confirmDetails.NewCustomPalletID = '';
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        //alert(data.Result);
                        //alert("3");
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
                
            };

            $scope.ProcessMediaSN = function (ev) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/pending_media_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ProcessMediaSN&'+$.param($scope.confirmDetails)+'&'+$.param($scope.CurrentPallet),
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                            );
                            $scope.hide();
                        } else {                            
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                            );
                        }                        
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        //alert(data.Result);
                        //alert("3");
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });



            };

            $scope.focusNextField = function (id) {
                $window.document.getElementById(id).focus();
                //$window.document.getElementById("Password").focus();
            };

        }
        $scope.CurrentPallet = {};
        $scope.confirmDetails = {};
        function afterShowAnimation () {            
            $window.document.getElementById("AuditController").focus();
            //$window.document.getElementById("nextbinid").focus();
            //$scope.GetCurrentTime($scope.confirmDetails,'serial_scan_time');
        }

        $scope.ValidateMediaProcessTPVRControllerPopup = function (pallet,ev) {   
            if($scope.filter_text[0].MediaSerialNumber != '' && $scope.filter_text[0].MediaSerialNumber) {

                if($scope.filter_text[0].MediaSerialNumber == pallet.MediaSerialNumber) {

                    //$scope.CurrentPallet = pallet;
                    //$scope.asset.PasswordVerified = false;
                    $mdDialog.show({
                        controller: QuarantineToActiveTPVRController,
                        templateUrl: 'password.html',
                        parent: angular.element(document.body),
                        targetEvent: ev,
                        onComplete: afterShowAnimation,
                        clickOutsideToClose:true,
                        resolve: {
                            CurrentPallet: function () {
                            return pallet;
                            }
                        }
                    })
                    .then(function(confirmDetails) {      
                        $scope.filter_text[0].MediaSerialNumber = '';
                        $scope.CallServerFunction($scope.currentPage);
                        /*$rootScope.$broadcast('preloader:active');
                        $scope.confirmDetails = confirmDetails;
                        jQuery.ajax({
                            url: host + 'receive/includes/receive_submit.php',
                            dataType: 'json',
                            type: 'post',
                            //data: 'ajax=ValidateSanitizationController&UserName='+$scope.asset.sanitization_controller_login_id+'&Password='+$scope.confirmDetails.Password,
                            data: 'ajax=ValidateAuditController&'+$.param($scope.confirmDetails),
                            success: function(data){
                                if(data.Success) {
                                    $scope.confirmDetails.PasswordVerified = true;
                                    //$window.document.getElementById('scan_for_save').focus();
                                    $scope.UpdatePallet(pallet,ev);
                                } else {
                                    $mdToast.show(
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-danger md-block')
                                    );
                                    $scope.confirmDetails.PasswordVerified = false;
                                }
                                $rootScope.$broadcast('preloader:hide');
                                initSessionTime(); $scope.$apply();
                            }, error : function (data) {
                                $scope.confirmDetails.PasswordVerified = false;
                                $rootScope.$broadcast('preloader:hide');
                                $scope.data = data;
                                initSessionTime(); $scope.$apply();
                            }
                        });*/

                    }, function(confirmDetails) {
                        $scope.confirmDetails = confirmDetails;
                    });

                } else {
                    $mdToast.show (
                        $mdToast.simple()
                        .content('Searched Media SN is different from Processed SN')                    
                        .position('right')
                        .hideDelay(3000)
                        .toastClass('md-toast-danger md-block')
                    );
                    $window.document.getElementById("filter_MediaSerialNumber").focus();    
                }
            }  else {
                $mdToast.show (
                    $mdToast.simple()
                    .content('Enter Media SN in Media SN Filter')                    
                    .position('right')
                    .hideDelay(3000)
                    .toastClass('md-toast-danger md-block')
                );
                $window.document.getElementById("filter_MediaSerialNumber").focus();
            }
        };

        $scope.GetHoursDifference = function (date) {

            var duration = moment.duration(moment(new Date()).diff(date));
            var hours = duration.asHours();
            return hours;

            return moment.duration(end.diff(date)).asHours();
            return moment(date).hours();
            moment().hours()
            return date;
        };


    });

    

})();
