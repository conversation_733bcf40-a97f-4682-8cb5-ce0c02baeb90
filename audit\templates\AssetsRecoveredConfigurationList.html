<div ng-controller = "AssetsRecoveredConfigurationList" class="page">    

    <div class="row ui-section mb-0">            
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="AssetsRecoveredConfigurationList = true;">
                            <div class="md-toolbar-tools" style="cursor: pointer;">                            
                                
                                <i ng-click="AssetsRecoveredConfigurationList = !AssetsRecoveredConfigurationList" class="material-icons md-primary" ng-show="AssetsRecoveredConfigurationList">keyboard_arrow_up</i>
                                <i ng-click="AssetsRecoveredConfigurationList = !AssetsRecoveredConfigurationList" class="material-icons md-primary" ng-show="! AssetsRecoveredConfigurationList">keyboard_arrow_down</i>
                                <span ng-click="AssetsRecoveredConfigurationList = !AssetsRecoveredConfigurationList">Asset Recovery Configuration List</span>
                                <div flex></div> 
                                <!-- <a href="#!/RecoverConfigurationList" ng-click="RecoverConfigurationListxls()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex; margin-right: 5px;">
                                   <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a> -->

                                <a href="#!/AssetsRecoveredConfiguration" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex;">
                                    <i class="material-icons">add</i> <span>Create New Asset Recovery Configuration</span>
                                </a>

                                <a href="#!/AssetsRecoveredConfiguration" class="md-button md-raised md-default btn-w-md dis_open2 mr-5"  style="display:none; margin-right: 5px;">
                                    <i class="material-icons">add</i> <span>Create New</span>
                                </a>
                            </div>
                        </md-toolbar>

                        <div class="row"  ng-show="AssetsRecoveredConfigurationList">
                            <div class="col-md-12">
                                <div class="col-md-12">
                                    <div ng-show="pagedItems" class="pull-right" style="margin-top: 20px;">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div> 
                                    <div class="table-responsive" style="overflow: auto;">

                                        
                                        
                                                    
                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                    <th style="min-width: 40px;">Edit</th>
                                                    <th style="min-width: 40px;">Delete</th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('disposition')" ng-class="{'orderby' : OrderBy == 'disposition'}">
                                                        <div style="min-width: 180px;">                               
                                                            Disposition From<i class="fa fa-sort pull-right" ng-show="OrderBy != 'disposition'"></i>                                 
                                                            <span ng-show="OrderBy == 'disposition'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('workflow')" ng-class="{'orderby' : OrderBy == 'workflow'}">                           
                                                        <div style="min-width: 140px;">                               
                                                            Workflow <i class="fa fa-sort pull-right" ng-show="OrderBy != 'workflow'"></i>                                    
                                                            <span ng-show="OrderBy == 'workflow'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                     <th style="cursor:pointer;" ng-click="MakeOrderBy('parttype')" ng-class="{'orderby' : OrderBy == 'parttype'}">                         
                                                        <div style="min-width: 140px;">                               
                                                            Part Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'parttype'"></i>                                  
                                                            <span ng-show="OrderBy == 'parttype'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('input')" ng-class="{'orderby' : OrderBy == 'input'}">                         
                                                        <div style="min-width: 180px;">                               
                                                            Evaluation Result <i class="fa fa-sort pull-right" ng-show="OrderBy != 'input'"></i>                                  
                                                            <span ng-show="OrderBy == 'input'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('dispositionto')" ng-class="{'orderby' : OrderBy == 'dispositionto'}">                         
                                                        <div style="min-width: 180px;">                               
                                                            Disposition To <i class="fa fa-sort pull-right" ng-show="OrderBy != 'dispositionto'"></i>                                  
                                                            <span ng-show="OrderBy == 'dispositionto'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Facility <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>                                  
                                                            <span ng-show="OrderBy == 'FacilityName'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('Status')" ng-class="{'orderby' : OrderBy == 'Status'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'Status'"></i>                                  
                                                            <span ng-show="OrderBy == 'Status'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('DefaultValue')" ng-class="{'orderby' : OrderBy == 'DefaultValue'}">                         
                                                        <div style="min-width: 80px;">                               
                                                            Default <i class="fa fa-sort pull-right" ng-show="OrderBy != 'DefaultValue'"></i>                                  
                                                            <span ng-show="OrderBy == 'DefaultValue'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>                                                    
                                                    
                                                </tr>
                                                
                                                <tr class="errornone">                        
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="disposition" ng-model="filter_text[0].disposition" ng-change="MakeFilter()"  aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="workflow" ng-model="filter_text[0].workflow" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="parttype" ng-model="filter_text[0].parttype" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>  
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="input" ng-model="filter_text[0].input" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>  
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="dispositionto" ng-model="filter_text[0].dispositionto" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>  
                                                    <td>
                                                        <!-- <md-input-container class="md-block mt-0">
                                                            <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container> -->
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="Status" ng-model="filter_text[0].Status" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>  
                                                    <td></td>                                                                       
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <td><a href="#!/AssetsRecoveredConfiguration/{{product.ConfigurationID}}">
                                                        <md-icon class="material-icons text-danger">edit</md-icon></a>
                                                    </td>
                                                    <td>                                                       
                                                        <i class="material-icons text-danger" ng-click="DeleteRecoverConfiguration(product)" role="button" tabindex="0">delete</i>
                                                    </td>
                                                    <td>
                                                        {{product.disposition}}                            
                                                    </td>                       
                                                    <td>
                                                        {{product.workflow}}
                                                    </td>
                                                    <td>
                                                        {{product.parttype}}
                                                    </td>
                                                    <td>
                                                        {{product.input}}
                                                    </td>
                                                    <td>
                                                        {{product.dispositionto}}
                                                    </td>
                                                    <td>
                                                        {{product.FacilityName}}
                                                    </td>
                                                    <td>
                                                        {{product.Status}}
                                                    </td>
                                                    <td>
                                                        <md-switch ng-model="product.DefaultValue" aria-label="Default" ng-true-value="'1'" ng-false-value="'0'" ng-change="MakeDefaultInputResult(product,$index,$event)"></md-switch>
                                                    </td>
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="10">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>     
                    
                    </md-card>

                </div>

            </article>
        </div>
    </div>

</div>