<div class="row page" data-ng-controller="TrackIntegration">
    <div class="col-md-12">
        <article class="article">
            <md-card class="no-margin-h"> 
                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Track Integrations</span>
                        <div flex></div>
                            <!-- <a ng-click="ExportTrackRecords(item.idPallet)" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                <md-icon class="excel_icon mr-5" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                            </a>                           -->
                    </div>
                </md-toolbar>
                <div class="row">
                    <form>
                        <div class="col-md-12">
                            <div class="col-md-4">
                                <md-input-container class="md-block">
                                    <label>Input Type</label>                                                                     
                                    <md-select name="InputType" ng-model="InputType" required aria-label="select" ng-change="TypeChanged()">
                                        <md-option  value="ASN"> ASN Information </md-option>
                                        <md-option  value="SNS"> SNS Information </md-option>
                                        <md-option  value="API"> Rack API Information </md-option>
                                    </md-select>                                                            
                                </md-input-container>
                            </div>

                            <div class="col-md-4">
                                <md-input-container class="md-block includedsearch" >
                                    <label>                                        
                                        {{GetInputText()}}
                                    </label>
                                    <input name="SearchInput" ng-model="SearchInput" ng-enter="TrackInput()" id="SearchInput" />
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" aria-label="Speed SN" ng-disabled="!SearchInput || !InputType">
                                        <md-icon md-svg-src="../assets/images/search.svg" ng-click="TrackInput()"></md-icon>
                                    </md-button>
                                </md-input-container>
                            </div>                
                            
                        </div>
                    </form>
                </div>
            </md-card>

            <md-card class="no-margin-h" ng-show="tracking.length >0">
                <md-card-content>
                    <div class="md-toolbar-tools" style="cursor: pointer;"> 
                   <!--  <h3>Track Results</h3>  -->
                    <span>Track Results</span> 
                    <div flex></div> 
                    <a href="#!/TrackIntegrations" ng-click="TrackIntegrationsxls()" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex; margin-right: 5px;">
                       <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                    </a>    
                    </div>              
                    <md-table-container ng-show="type == 'SNS'">
                        <table md-table class="table mb-0 multi-tables">
                            <thead md-head>
                                <tr md-row>                                   
                                    <th md-column>Rack ID</th>
                                    <th md-column>Host Asset ID</th>
                                    <th md-column>Media Serial</th>                                    
                                    <th md-column>Event Type</th>
                                    <th md-column>Message ID</th>
                                    <th md-column>Date</th>
                                    <th md-column>Created By</th>                                                                
                                </tr>
                            </thead>
                            <tbody md-body ng-repeat="item in tracking">
                                <tr md-row >                                    
                                    <td md-cell>{{item.idPallet}}</td>
                                    <td md-cell>{{item.ServerSerialNumber}}</td>
                                    <td md-cell>{{item.MediaSerialNumber}}</td>                                    
                                    <td md-cell>{{item.event_type}}</td>
                                    <td md-cell>{{item.MessageID}}</td>
                                    <td md-cell>{{item.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                                    <td md-cell>{{item.FirstName}} {{item.LastName}}</td>
                                </tr>                                
                            </tbody>
                        </table>
                    </md-table-container>


                    <md-table-container ng-show="type == 'API'">
                        <table md-table class="table mb-0 multi-tables">
                            <thead md-head>
                                <tr md-row>                                   
                                    <th md-column>Rack ID</th>
                                    <th md-column>Result</th>                                   
                                    <th md-column>Output</th>
                                    <th md-column>Date</th>
                                    <th md-column>Created By</th>                                                                                                                                                   
                                </tr>
                            </thead>
                            <tbody md-body ng-repeat="item in tracking">
                                <tr md-row >                                    
                                    <td md-cell>{{item.idPallet}}</td>
                                    <td md-cell>{{item.Result}}</td>
                                    <td md-cell>{{item.Output}}</td>                                                                        
                                    <td md-cell>{{item.APICalledDATETIME | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                                    <td md-cell>{{item.FirstName}} {{item.LastName}}</td>
                                </tr>                                
                            </tbody>
                        </table>
                    </md-table-container>


                    <!-- <md-table-container ng-show="type == 'ASN'">
                        <table md-table class="table mb-0 multi-tables">
                            <thead md-head>
                                <tr md-row>      
                                    <th md-column>Ticket ID</th>                             
                                    <th md-column>Rack ID</th>
                                    <th md-column>SN</th>                                   
                                    <th md-column>MPN</th>
                                    <th md-column>Part Type</th>
                                    <th md-column>Created Date</th>                                                                                                                                                   
                                    <th md-column>Serial Received</th>
                                </tr>
                            </thead>
                            <tbody md-body ng-repeat="item in tracking">
                                <tr md-row >  
                                    <td md-cell>{{item.LoadId}}</td>                                  
                                    <td md-cell>{{item.idPallet}}</td>
                                    <td md-cell>{{item.SerialNumber}}</td>
                                    <td md-cell>{{item.UniversalModelNumber}}</td>     
                                    <td md-cell>{{item.part_type}}</td>                                                                        
                                    <td md-cell>{{item.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>                                    
                                    <td md-cell>
                                        <span ng-show="item.AssetScanID > 0">Yes  (Received Date {{item.AssetCreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}) </span>
                                        <span ng-show="item.AssetScanID == '' || item.AssetScanID == null">No</span>
                                    </td>
                                </tr>                                
                            </tbody>
                        </table>
                    </md-table-container> -->

                     <div class="row"  ng-show="type == 'ASN'">
                            <div class="col-md-12">
                                <div class="col-md-12">

                                    <div ng-show="tracking.length > 0" class="pull-right tracking">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span> 
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>   
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div> 
                                    <div class="table-responsive" style="overflow: auto;">

                                        <table class="table table-striped">
                                            <thead>
                                                <tr class="th_sorting">
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('LoadId')" ng-class="{'orderby' : OrderBy == 'LoadId'}">
                                                        <div>                               
                                                            Ticket ID<i class="fa fa-sort pull-right" ng-show="OrderBy != 'LoadId'"></i>                                 
                                                            <span ng-show="OrderBy == 'LoadId'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('idPallet')" ng-class="{'orderby' : OrderBy == 'idPallet'}">                           
                                                        <div style="min-width: 180px;">                               
                                                            Rack ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'idPallet'"></i>                                    
                                                            <span ng-show="OrderBy == 'idPallet'">                                 
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('SerialNumber')" ng-class="{'orderby' : OrderBy == 'SerialNumber'}">                         
                                                        <div style="min-width: 140px;">                               
                                                            SN<i class="fa fa-sort pull-right" ng-show="OrderBy != 'SerialNumber'"></i>                                  
                                                            <span ng-show="OrderBy == 'SerialNumber'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('UniversalModelNumber')" ng-class="{'orderby' : OrderBy == 'UniversalModelNumber'}">                         
                                                        <div style="min-width: 140px;">                               
                                                            MPN <i class="fa fa-sort pull-right" ng-show="OrderBy != 'UniversalModelNumber'"></i>                                  
                                                            <span ng-show="OrderBy == 'UniversalModelNumber'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('part_type')" ng-class="{'orderby' : OrderBy == 'part_type'}">                         
                                                        <div style="min-width: 140px;">                               
                                                            Part Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'part_type'"></i>                                  
                                                            <span ng-show="OrderBy == 'part_type'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('CreatedDate')" ng-class="{'orderby' : OrderBy == 'CreatedDate'}">                         
                                                        <div style="min-width: 180px;">                               
                                                            Created Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'CreatedDate'"></i>                                  
                                                            <span ng-show="OrderBy == 'CreatedDate'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('AssetCreatedDate')" ng-class="{'orderby' : OrderBy == 'AssetCreatedDate'}">                         
                                                        <div style="min-width: 150px;">                               
                                                            Serial Received <i class="fa fa-sort pull-right" ng-show="OrderBy != 'AssetCreatedDate'"></i>                                  
                                                            <span ng-show="OrderBy == 'AssetCreatedDate'">                                    
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>                                  
                                                            </span>                                                                                                                                                             
                                                        </div>                                                                                  
                                                    </th>

                                                    
                                                </tr>
                                                
                                                <tr class="errornone">                        
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="LoadId" ng-model="filter_text[0].LoadId" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="idPallet" ng-model="filter_text[0].idPallet" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="SerialNumber" ng-model="filter_text[0].SerialNumber" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="UniversalModelNumber" ng-model="filter_text[0].UniversalModelNumber" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="part_type" ng-model="filter_text[0].part_type" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="CreatedDate" ng-model="filter_text[0].CreatedDate" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td> 
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="AssetCreatedDate" ng-model="filter_text[0].AssetCreatedDate" ng-change="MakeFilter()" aria-label="text" />
                                                        </md-input-container>
                                                    </td>  
                                                </tr>
                                            </thead>
                                            
                                            <tbody ng-show="tracking.length > 0">
                                                <tr ng-repeat="item in tracking">
                                                    <td>{{item.LoadId}}</td>
                                                    <td>{{item.idPallet}}</td>
                                                    <td>{{item.SerialNumber}}</td>
                                                    <td>{{item.UniversalModelNumber}}</td>
                                                    <td>{{item.part_type}}</td>
                                                    <td>{{item.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>                                    
                                                    <td>
                                                        <span ng-show="item.AssetScanID > 0">Yes  (Received Date {{item.AssetCreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}) </span>
                                                        <span ng-show="item.AssetScanID == '' || item.AssetScanID == null">No</span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                            
                                            <tfoot>
                                                <tr>
                                                    <td colspan="8">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>   
                                                </tr>             
                                            </tfoot>

                                        </table>                            
                                    </div>
                                </div>
                            </div>
                        </div>                   
                </md-card-content>
            </md-card>
        </article>        
    </div>
</div>