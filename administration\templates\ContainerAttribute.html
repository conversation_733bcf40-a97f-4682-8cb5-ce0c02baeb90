
<div class="row page" data-ng-controller="ContainerAttribute">
    <div class="col-md-12">
        <article class="article">

            <md-card class="no-margin-h">

                <md-toolbar class="md-table-toolbar md-default">
                    <div class="md-toolbar-tools">
                        <span>Re-Assign Container</span>
                        <div flex></div>
                            <!-- <a href="#!/ContainerAttributeList" class="md-button md-raised btn-w-md" style="display: flex;">
                              <i class="material-icons">chevron_left</i> Back To List
                            </a> -->
                    </div>
                </md-toolbar>

                <div class="row">
                    <form name="material_signup_form" class="form-validation" data-ng-submit="submitForm()">

                        <!--1st row start-->
                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block includedsearch">
                                    <label>Scan Inbound Container ID</label>
                                    <input type="text" name="searchidPallet" ng-model="searchidPallet" id="searchidPallet"  ng-maxlength="100" ng-enter="GetInboundContainers()" required/>
                                    <md-button class="md-fab md-raised md-accent md-mini md-fab-bottom-right" type="button" aria-label="searchidPallet" ng-click="GetInboundContainers()" ng-disabled="!searchidPallet">
                                    <md-icon md-svg-src="../assets/images/search.svg"></md-icon>
                                    </md-button>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.searchidPallet.$error" multiple ng-if='material_signup_form.searchidPallet.$dirty'>
                                            <div ng-message="required">This is required.</div>
                                            <div ng-message="minlength">Min length 3.</div>
                                            <div ng-message="maxlength">Max length 100.</div>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center"></div>

                            <div class="col-md-3"></div>

                        </div>
                        <!--1st row Close-->

                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display Current Material Type</label>
                                   <input type="text" name="MaterialType"  ng-model="reassigndetails['MaterialType']"  data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.CurrentMaterialTypeCB" ng-disabled ="!reassigndetails.MaterialType" ng-change="EnableSave(reassign.CurrentMaterialTypeCB)"></md-checkbox>
                                </md-input-container>
                            </div>
                            <div class="col-md-3">
                                <md-input-container class="md-block includedsearch">
                                    <label>Select New Material Type</label>
                                    <md-select name="MaterialTypeID" ng-model="reassign.MaterialTypeID" aria-label="select" ng-disabled ="!reassign.CurrentMaterialTypeCB">
                                        <md-option ng-repeat="material in AWSMaterialTypes" value="{{material.MaterialTypeID}}"> {{material.MaterialType}} </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.MaterialType.$error" multiple ng-if='material_signup_form.MaterialType.$dirty'>

                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                        </div>

                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display Current Source</label>
                                    <input type="text" name="CustomerName"  ng-model="reassigndetails['CustomerName']"  data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.CurrentSourceTypeCB" ng-disabled ="!reassigndetails.CustomerName" ng-change="EnableSave(reassign.CurrentSourceTypeCB)"></md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Select New Source</label>
                                        <!--<md-select name="idCustomertype" ng-model="reassign.idCustomertype" aria-label="select" ng-disabled ="!reassign.CurrentSourceTypeCB">
                                            <md-option ng-repeat="st in SourceTypes" value="{{st.idCustomertype}}"> {{st.Cumstomertype}} </md-option>
                                        </md-select>-->
                                      <md-select name="idCustomer" ng-model="reassign.idCustomer" aria-label="select" ng-disabled ="!reassign.CurrentSourceTypeCB">
                                                <md-option ng-repeat="cus in Customer" value="{{cus.CustomerID}}"> {{cus.CustomerName}} </md-option>
                                      </md-select>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.idCustomer.$error" multiple ng-if='material_signup_form.idCustomer.$dirty'>

                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                        </div>

                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display Current Weight</label>
                                    <input type="text" name="pallet_netweight"  ng-model="reassigndetails['pallet_netweight']"  data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.CurrentWeightCB" ng-disabled ="!reassigndetails.pallet_netweight" ng-change="EnableSave(reassign.CurrentWeightCB)"></md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block tdinput">
                                    <label>Enter New Weight</label>
                                    <input type="number" name="pallet_netweight" ng-model="reassign.pallet_netweight"  ng-disabled ="!reassign.CurrentWeightCB">
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.pallet_netweight.$error" multiple ng-if='material_signup_form.pallet_netweight.$dirty'>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>

                        </div>

                        <div class="col-md-12">

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Display Current Facility</label>
                                    <input type="text" name="FacilityName"  ng-model="reassigndetails['FacilityName']"  data-ng-disabled="true" />
                                    <div class="error-space"></div>
                                </md-input-container>
                            </div>

                            <div class="col-md-2 text-center">
                                <md-input-container class="md-block" >
                                    <md-checkbox ng-model="reassign.DisplayFacilityCB" ng-disabled ="!reassigndetails.FacilityName" ng-change="EnableSave(reassign.DisplayFacilityCB)"></md-checkbox>
                                </md-input-container>
                            </div>

                            <div class="col-md-3">
                                <md-input-container class="md-block">
                                    <label>Select New Facility</label>
                                    <md-select name="FacilityID" ng-model="reassign.FacilityID" aria-label="select" ng-disabled ="!reassign.DisplayFacilityCB">
                                        <md-option ng-repeat="facilityinformation in Facility" value="{{facilityinformation.FacilityID}}"> {{facilityinformation.FacilityName}} </md-option>
                                    </md-select>
                                    <div class="error-space">
                                        <div class="md-errors-spacer" ng-messages="material_signup_form.FacilityID.$error" multiple ng-if='material_signup_form.FacilityID.$dirty'>
                                        </div>
                                    </div>
                                </md-input-container>
                            </div>
                            <div class="col-md-3" >
                                <div class="autocomplete insideuse">
                                     <md-input-container class="md-block">
                                         <md-autocomplete flex  style="margin-bottom:0px !important; margin-top:0px !important; padding-top: 0px !important;"
                                            md-input-name="location"
                                            md-input-maxlength="100"
                                            ng-disabled="reassign.FacilityID == 0 || !reassign.FacilityID"
                                            md-no-cache="noCache"
                                            md-search-text-change="LocationChange(reassign.group)"
                                            md-search-text="reassign.group"
                                            md-items="item in queryLocationSearch(reassign.group)"
                                            md-item-text="item.GroupName"
                                            md-selected-item-change="selectedLocationChange(item)"
                                            md-min-length="0"
                                            placeholder="Search Location Group">
                                            <md-item-template>
                                                <span md-highlight-text="reassign.group" md-highlight-flags="^i">{{item.GroupName}}</span>
                                            </md-item-template>
                                            <md-not-found>
                                                No Records matching "{{reassign.group}}" were found.
                                            </md-not-found>
                                            <div ng-messages="material_signup_form.group.$error" ng-if="material_signup_form.group.$touched">
                                                <div ng-message="required">No Records matching.</div>
                                            </div>
                                        </md-autocomplete>
                                    </md-input-container>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="col-md-12 btns-row">
                                <md-button class="md-button md-raised btn-w-md  md-default" ng-click="CancelContainerAttribute()">
                                    Cancel
                                </md-button>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                data-ng-disabled="material_signup_form.$invalid || reassign.busy || disableSave" ng-click="CreateContainerAttribute()">
                                <span ng-show="! reassign.busy">Save</span>
                                <span ng-show="reassign.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span></md-button>
                                <a href="{{host}}label/master/examples/ContainerAttribute.php?id={{searchidPallet}}" target="_blank">
                                    <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                    data-ng-disabled="material_signup_form.$invalid || reassign.busy">
                                    <span ng-show="! reassign.busy">Print</span>
                                    <span ng-show="reassign.busy"><md-progress-circular class="md-hue-2" md-mode="indeterminate" md-diameter="20px" style="left:50px;"></md-progress-circular></span>
                                    </md-button>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </md-card>
        </article>
    </div>
</div>
