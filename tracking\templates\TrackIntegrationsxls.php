<?php
session_start();
include_once("../../config.php");
$curr = CURRENCY;
$weight = WEIGHT;
$dateformat = DATEFORMAT;
$data = $_SESSION['TrackIntegrationsxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "TrackIntegration.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect1();
$datatoday = array('Generated Date',$today);
$datahead = array('Track Integration');
$rows = array();

if($data['InputType'] == 'SNS') {
    $json['Type'] = $data['InputType'];
    $querytrack = "select m.*,u.FirstName,u.LastName from speed_sns_messages m left join users u on m.CreatedBy = u.UserId  where (m.MediaSerialNumber = '".mysqli_real_escape_string($connectionlink,$data['SearchInput'])."' or m.ServerSerialNumber = '".mysqli_real_escape_string($connectionlink,$data['SearchInput'])."')";
    $qtrack = mysqli_query($connectionlink1,$querytrack);
    if(mysqli_error($connectionlink1)) {
        echo mysqli_error($connectionlink1);    
    }
     while($rowtrack = mysqli_fetch_assoc($qtrack))
        {
            $dt = strtotime($rowtrack['CreatedDate']);
            $rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);

            $createby = $rowtrack['FirstName']." ".$rowtrack['LastName'];

            $row2  = array($rowtrack['idPallet'],$rowtrack['ServerSerialNumber'],$rowtrack['MediaSerialNumber'],$rowtrack['event_type'],$rowtrack['MessageID'],$rowtrack['CreatedDate'],$createby);
            $rows[] = $row2;
             
        }

    $querytrack = "select m.*,u.FirstName,u.LastName from speed_sns_messages m left join users u on m.CreatedBy = u.UserId where m.idPallet = '".mysqli_real_escape_string($connectionlink,$data['SearchInput'])."' and isnull(MediaSerialNumber) and isnull(ServerSerialNumber) ";
    $qtrack = mysqli_query($connectionlink1,$querytrack);


        while($rowtrack = mysqli_fetch_assoc($qtrack))
        {
            $dt = strtotime($rowtrack['CreatedDate']);
            $rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);

            $createby = $rowtrack['FirstName']." ".$rowtrack['LastName'];

            $row2  = array($rowtrack['idPallet'],$rowtrack['ServerSerialNumber'],$rowtrack['MediaSerialNumber'],$rowtrack['event_type'],$rowtrack['MessageID'],$rowtrack['CreatedDate'],$createby);
            $rows[] = $row2;
             
        }
    $header = array('Rack ID','Host Asset ID','Media Serial','Event Type','Message ID','Date','Created By');
    $sheet_name = 'Track Integration';
    $style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
    $writer = new XLSWriterPlus();
    $writer->setAuthor('eViridis');
    $writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
    $writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
    $writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
    $writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
    $writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
    $writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
    foreach($rows as $row11)
        $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
    $writer->writeToStdOut();
}


if($data['InputType'] == 'API') {
    $json['Type'] = $data['InputType'];
    $sql = "select r.*,u.FirstName,u.LastName from speed_rackdetails_api r left join users u on r.APICalledBy = u.UserId where r.idPallet = '".mysqli_real_escape_string($connectionlink,$data['SearchInput'])."'";
    $query = mysqli_query($connectionlink1,$sql);
    if(mysqli_error($connectionlink1)) {
        echo mysqli_error($connectionlink1);    
    }

        while($row = mysqli_fetch_assoc($query))
        {
            $createby = $row['FirstName']." ".$row['LastName'];
            $row2  = array($row['idPallet'],$row['Result'],$row['Output'],$row['APICalledDATETIME'],$createby);
            $rows[] = $row2;
             
        }
    $header = array('Rack ID','Result','Output','Date','Created By');
    $sheet_name = 'Track Integration';
    $style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
    $writer = new XLSWriterPlus();
    $writer->setAuthor('eViridis');
    $writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
    $writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
    $writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
    $writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
    $writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
    $writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
    foreach($rows as $row11)
        $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
    $writer->writeToStdOut();
}

if($data['InputType'] == 'ASN') {
    $json['Type'] = $data['InputType'];
    $sql = "select r.LoadId,r.idPallet,r.SerialNumber,r.UniversalModelNumber,r.AssetScanID,r.CreatedDate,r.AssetCreatedDate,r.part_type from asn_assets r where r.idPallet = '".mysqli_real_escape_string($connectionlink1,$data['SearchInput'])."'";
    if($data[0] && count($data[0]) > 0) {
                foreach ($data[0] as $key => $value) {
                    if($value != '') {
                        if($key == 'LoadId') {
                            $sql = $sql . " AND r.LoadId like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }                   
                        if($key == 'idPallet') {
                            $sql = $sql . " AND r.idPallet like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }
                        if($key == 'SerialNumber') {
                            $sql = $sql . " AND r.SerialNumber like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        } 
                        if($key == 'UniversalModelNumber') {
                            $sql = $sql . " AND r.UniversalModelNumber like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        } 
                        if($key == 'part_type') {
                            $sql = $sql . " AND r.part_type like '%".mysqli_real_escape_string($connectionlink1,$value)."%' ";
                        }                      
                    }
                }
            }
            if($data['OrderBy'] != '') {
                if($data['OrderByType'] == 'asc') {
                    $order_by_type = 'asc';
                } else {
                    $order_by_type = 'desc';
                }

                if($data['OrderBy'] == 'LoadId') {
                    $sql = $sql . " order by m.ManufacturerName ".$order_by_type." ";
                } else if($data['OrderBy'] == 'idPallet') {
                    $sql = $sql . " order by r.idPallet ".$order_by_type." ";
                } else if($data['OrderBy'] == 'SerialNumber') {
                    $sql = $sql . " order by r.SerialNumber ".$order_by_type." ";
                } else if($data['OrderBy'] == 'UniversalModelNumber') {
                    $sql = $sql . " order by r.UniversalModelNumber ".$order_by_type." ";
                } else if($data['OrderBy'] == 'part_type') {
                    $sql = $sql . " order by r.part_type ".$order_by_type." ";
                }            
            } else {
                //$sql = $sql . " order by r.idPallet desc ";
            }

    $query = mysqli_query($connectionlink1,$sql);
    if(mysqli_error($connectionlink1)) {
        echo mysqli_error($connectionlink1);    
    }

        while($row = mysqli_fetch_assoc($query))
        {
            $row2  = array($row['LoadId'],$row['idPallet'],$row['SerialNumber'],$row['UniversalModelNumber'],$row['part_type'],$row['CreatedDate'],$row['AssetCreatedDate']);
            $rows[] = $row2;
        }
}
$header = array('Ticket ID','Rack ID','SN','MPN','Part Type','Created Date','Serial Received');
$sheet_name = 'Track Integration';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 7);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?> 