<?php

try {
    $curl = curl_init();

    curl_setopt_array($curl, array(
    CURLOPT_URL => 'https://awsapiprod.eviridis.com/genai/BlogGeneration',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 0,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => 'POST',
    CURLOPT_POSTFIELDS =>'{"Query": "What is shipping container ?"}',
    CURLOPT_HTTPHEADER => array(
        'Content-Type: application/json'
    ),
    ));

    $response = curl_exec($curl);

    curl_close($curl);
    echo $response;
 
} catch (Exception $ex) {
    echo $ex->getMessage();			
}

?>