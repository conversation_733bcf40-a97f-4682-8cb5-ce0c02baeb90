<?php
session_start();
include_once("../connection.php");
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();

$query= "select * from shipping_container_serials";

$query = "SELECT s.*,c.StatusID FROM shipping_container_serials s 
left join shipping_containers c on s.ShippingContainerID = c.ShippingContainerID 
 where c.StatusID != 3 and isnull(s.COOID)";
$q = mysqli_query($connectionlink,$query);
if(mysqli_affected_rows($connectionlink) > 0) {
    $i = 0;
    while($row = mysqli_fetch_assoc($q)) {
        if($row['AssetScanID'] != '')
        {
            $sqlcoo = "Select CO.COO,A.COOID from asset A,COO CO
            where 
            CO.COOID = A.COOID
            AND A.AssetScanID = '".$row['AssetScanID']."'";
            $querycoo = mysqli_query($connectionlink,$sqlcoo);
            $rowcoo = mysqli_fetch_assoc($querycoo);
            if($rowcoo['COOID'] > 0) {
                $query3 = "update shipping_container_serials set COO = '".$rowcoo['COO']."',COOID = '".$rowcoo['COOID']."' where AssetScanID = '".$row['AssetScanID']."'";
                $q3 = mysqli_query($connectionlink,$query3);	
                if(mysqli_error($connectionlink)) {			
                    $json['Success'] = false;		
                    echo mysqli_error($connectionlink)."<br>";
                    break;			
                }
            }            
        }
        else if($row['InventoryID'] != '')
        {
            $sqlcoo = "Select CO.COO,A.COOID from inventory A,COO CO
            where 
            CO.COOID = A.COOID
            AND A.InventoryID = '".$row['InventoryID']."'";
            $querycoo = mysqli_query($connectionlink,$sqlcoo);
            $rowcoo = mysqli_fetch_assoc($querycoo);
            if($rowcoo['COOID'] > 0) {
                $query3 = "update shipping_container_serials set COO = '".$rowcoo['COO']."',COOID = '".$rowcoo['COOID']."' where InventoryID = '".$row['InventoryID']."'";
                $q3 = mysqli_query($connectionlink,$query3);	
                if(mysqli_error($connectionlink)) {			
                    $json['Success'] = false;		
                    echo mysqli_error($connectionlink)."<br>";
                    break;			
                }
            }
        }
        else if($row['ServerID'] != '')
        {
            $sqlcoo = "Select CO.COO,A.COOID from speed_server_recovery A,COO CO
            where 
            CO.COOID = A.COOID
            AND A.ServerID = '".$row['ServerID']."'";
            $querycoo = mysqli_query($connectionlink,$sqlcoo);
            $rowcoo = mysqli_fetch_assoc($querycoo);
            if($rowcoo['COOID'] > 0) {
                $query3 = "update shipping_container_serials set COO = '".$rowcoo['COO']."',COOID = '".$rowcoo['COOID']."' where ServerID = '".$row['ServerID']."'";
                $q3 = mysqli_query($connectionlink,$query3);	
                if(mysqli_error($connectionlink)) {			
                    $json['Success'] = false;		
                    echo mysqli_error($connectionlink)."<br>";
                    break;			
                }
            }
        }
        else if($row['MediaID'] != '')
        {
            $sqlcoo = "Select CO.COO,A.COOID from speed_media_recovery A,COO CO
            where 
            CO.COOID = A.COOID
            AND A.MediaID = '".$row['MediaID']."'";
            $querycoo = mysqli_query($connectionlink,$sqlcoo);
            $rowcoo = mysqli_fetch_assoc($querycoo);
            if($rowcoo['COOID'] > 0) {
                $query3 = "update shipping_container_serials set COO = '".$rowcoo['COO']."',COOID = '".$rowcoo['COOID']."' where MediaID = '".$row['MediaID']."'";
                $q3 = mysqli_query($connectionlink,$query3);	
                if(mysqli_error($connectionlink)) {			
                    $json['Success'] = false;		
                    echo mysqli_error($connectionlink)."<br>";
                    break;			
                }
            }
        }
        $i++;
    }
    echo $i. "Records Completed";
} else {
    echo "No more records available";
}