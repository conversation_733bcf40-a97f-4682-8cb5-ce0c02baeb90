<?php
session_start();
include_once("../../config.php");
$dateformat = DATEFORMAT;
$data = $_SESSION['idPallet'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "ExportTrackRecords.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');

$SerialNumber = $_GET['SerialNumber'];
$ID = $_GET['ID'];
$Type = $_GET['Type'];

include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
//$datatoday = array('Generated Date',$today);
$header = array('Serial Number','Rack ID','MPN','Part Type','Disposition','Recovery Type');
$datahead = array('Tracking Serials');
$rows = array();

if($Type == 'Media') {
	$query0 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype,A.MediaType from speed_media_recovery A
	LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
	LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
	where A.MediaID = '".mysqli_real_escape_string($connectionlink,$ID)."' ";
	$q0 = mysqli_query($connectionlink,$query0);

	if(mysqli_affected_rows($connectionlink) > 0) {
		while($row0 = mysqli_fetch_assoc($q0)) {
			$row  = array($row0['SerialNumber'],$row0['idPallet'],$row0['UniversalModelNumber'],$row0['MediaType'],$row0['disposition'],'Assembly');
			$rows[] = $row;			
		}
	}
}

if($Type == 'Server') {
	$query0 = "select A.ServerID,A.ServerSerialNumber as SerialNumber,A.idPallet,A.MPN as UniversalModelNumber,D.disposition,pt.parttype,A.Type from speed_server_recovery A
	LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
	LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
	where A.ServerID = '".mysqli_real_escape_string($connectionlink,$ID)."' ";
	$q0 = mysqli_query($connectionlink,$query0);

	if(mysqli_affected_rows($connectionlink) > 0) {
		while($row0 = mysqli_fetch_assoc($q0)) {
			$row  = array($row0['SerialNumber'],$row0['idPallet'],$row0['UniversalModelNumber'],$row0['Type'],$row0['disposition'],'Assembly');
			$rows[] = $row;			
		}
	}
}

if($Type == 'Rack') {
	$row  = array($ID,$ID,'','Rack','','');
	$rows[] = $row;
}


//Start get next level
if($Type == 'Server') {// Get all Media Details
	$query1 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype,A.MediaType from speed_media_recovery A
	LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
	LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
	where A.ServerSerialNumber = '".mysqli_real_escape_string($connectionlink,$SerialNumber)."' ";
	$q1 = mysqli_query($connectionlink,$query1);

	if(mysqli_affected_rows($connectionlink) > 0) {
		while($row1 = mysqli_fetch_assoc($q1)) {
			$row  = array($row1['SerialNumber'],$row1['idPallet'],$row1['UniversalModelNumber'],$row1['MediaType'],$row1['disposition'],'Rack');
			$rows[] = $row;			
		}
	}
}

if($Type == 'Rack') { //start get All servers from the Rack
	$query1 = "select A.ServerID,A.ServerSerialNumber as SerialNumber,A.idPallet,A.MPN as UniversalModelNumber,D.disposition,pt.parttype,A.Type from speed_server_recovery A
	LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
	LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
	where A.idPallet = '".mysqli_real_escape_string($connectionlink,$ID)."' ";
	$q1 = mysqli_query($connectionlink,$query1);

	if(mysqli_affected_rows($connectionlink) > 0) {
		while($row1 = mysqli_fetch_assoc($q1)) {
			$row  = array($row1['SerialNumber'],$row1['idPallet'],$row1['UniversalModelNumber'],$row1['Type'],$row1['disposition'],'Rack');
			$rows[] = $row;			
			//Start get All Media from the Server

			$query2 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype,A.MediaType from speed_media_recovery A
			LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
			LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
			where A.ServerSerialNumber = '".mysqli_real_escape_string($connectionlink,$row1['SerialNumber'])."' ";
			$q2 = mysqli_query($connectionlink,$query2);

			if(mysqli_affected_rows($connectionlink) > 0) {
				while($row2 = mysqli_fetch_assoc($q2)) {
					$row  = array($row2['SerialNumber'],$row2['idPallet'],$row2['UniversalModelNumber'],$row2['MediaType'],$row2['disposition'],'Assembly');
					$rows[] = $row;			
				}
			}

		}
	}
}


/*$query1 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
where A.idPallet = '".mysqli_real_escape_string($connectionlink,$data)."' ";
$q1 = mysqli_query($connectionlink,$query1);
if(mysqli_affected_rows($connectionlink) > 0) {
	while($row1 = mysqli_fetch_assoc($q1)) {
		$row2  = array($row1['SerialNumber'],$row1['idPallet'],$row1['UniversalModelNumber'],$row1['parttype'],$row1['disposition'],'Assembly');
		$rows[] = $row2;			
	}
}

$query2 = "select A.ServerID,A.ServerSerialNumber as SerialNumber,A.idPallet,A.MPN as UniversalModelNumber,D.disposition,pt.parttype from speed_server_recovery A
	LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
	LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
	where A.idPallet = '".mysqli_real_escape_string($connectionlink,$data)."' ";
$q2 = mysqli_query($connectionlink,$query2);
if(mysqli_affected_rows($connectionlink) > 0) {
	while($row3 = mysqli_fetch_assoc($q2)) {
		$row4  = array($row3['SerialNumber'],$row3['idPallet'],$row3['UniversalModelNumber'],$row3['parttype'],$row3['disposition'],'Rack');
		$rows[] = $row4;
	}
}*/

//$datatoday = array('Generated Date',$query1);
$sheet_name = 'Tracking Serials';
$style1 = array( ['font-style'=>'bold'],['font-style'=>''],['valign'=>'left']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 5);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
//$writer->writeSheetRow($sheet_name, $datatoday, $style1);
//$writer->writeSheetRow($sheet_name, $palletid1, $style1);
//$writer->writeSheetRow($sheet_name, $sealno, $style1);
//$writer->writeSheetRow($sheet_name, $location, $style1);
//$writer->writeSheetRow($sheet_name, $recieveddate, $style1);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
$writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom']);
$writer->writeToStdOut();
//exit(0);
?>
