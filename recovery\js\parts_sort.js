(function () {
    'use strict';

    angular.module('app').controller("PartsSort", function ($scope,$http,$filter,$upload,$rootScope,$mdToast,$mdDialog,$stateParams,$window) {
        $scope.asset = {};
        $scope.CustomPalletID = '';
        $scope.InputResults = [];
        $scope.Stations = [];
        $scope.StationCustomPallets = [];
        $scope.Rigs = [];
        $scope.DefaultInputID = '';
        $scope.DefaultRigID = '';
        $scope.Assets = [];
        $scope.GroupStations = [];
        $scope.COOList = [];
        $scope.SortHistory = [];
        $scope.SortCriteria = [];
        $scope.FromBin = {};

                $scope.CurrentBin = {};

        $scope.showAdvancedDiForm = function (ev, Bin) {
            $scope.CurrentBin = Bin;
            $mdDialog.show({
                controller: DialogControllerForm,
                templateUrl: '../recovery/templates/SortConfigurationBinFormChange.tmpl.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true,
                resolve: {
                    CurrentBin: function () {
                        return Bin;
                    }
                }
                //fullscreen: $scope.customFullscreen // Only for -xs, -sm breakpoints.
            })
            .then(function(BinDetails){
              $scope.asset.to_BinName = BinDetails.to_BinName;
              $scope.asset.to_CustomPalletID = BinDetails.to_CustomPalletID;
            },function(){

            });
        };

        function DialogControllerForm($scope, $mdDialog, CurrentBin, $mdToast) {
            $scope.CurrentBin = CurrentBin;
            $scope.hide = function () {
                $mdDialog.hide($scope.CurrentBin);
            };

            $scope.cancel = function () {
                $mdDialog.cancel();
            };

            $scope.answer = function (answer) {
                $mdDialog.hide(answer);
            };

            $scope.CurrentNewBin = { 'group': '', 'NewBinName': ''};

            $scope.isValidBinSelected = function () {

                if (($scope.CurrentNewBin.group != '') && ($scope.CurrentNewBin.NewBinName != '')) {
                    return false;
                } else {
                    return true;
                }
            };

            $scope.CreateMoveBin = function () {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'administration/includes/admin_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CreateMoveBin&sortconfigurationid=' + $scope.CurrentBin.sortconfigurationid + '&CurrentBin=' +$scope.CurrentBin.to_BinName + '&CurrentLocationName=' +$scope.CurrentBin.LocationID + '&CurrentBinID=' +$scope.CurrentBin.to_CustomPalletID + '&NewBin=' +$scope.CurrentNewBin.NewBinName + '&NewBinID=' +$scope.CurrentNewBin.CustomPalletID + '&NewLocationName=' +$scope.CurrentNewBin.group,
                    success: function (data) {
                        if (data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-success')
                            );
                            $scope.CurrentBin.to_BinName = $scope.CurrentNewBin.NewBinName;
                            $scope.CurrentBin.to_CustomPalletID = $scope.CurrentNewBin.CustomPalletID;
                            $scope.hide();

                            //$scope.CurrentBin.to_BinName = data.to_BinName;
                            //$scope.GetMPNFromSerial();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-danger')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        $scope.$apply();
                    }, error: function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        $scope.data = data;
                        $scope.$apply();
                    }
                });

            };

            function LocationChange(text) {
                $scope.CurrentBin.group = text;
            }

            function selectedLocationChange(item) {
                if(item) {
                    if(item.value) {
                        $scope.CurrentBin.group = item.value;
                        //$scope.SelectSortBin();
                    } else {
                        $scope.CurrentBin.group = '';
                    }
                } else {
                    $scope.CurrentBin.group = '';
                }
                console.log('Item changed to ' + JSON.stringify(item));
            }

            $scope.queryLocationSearch   = queryLocationSearch;
            $scope.LocationChange   = LocationChange;
            $scope.selectedLocationChange   = selectedLocationChange;
            function queryLocationSearch (query) {
                if(query) {
                    if(query != '' && query != 'undefined') {
                        //return $http.get(host+'administration/includes/admin_submit.php?ajax=GetMatchingLocations&keyword='+query+'&FacilityID='+$scope.cpalletForm.FacilityID)
                        return $http.get(host + 'administration/includes/admin_submit.php?ajax=GetMatchingLocationGroupsBin&keyword=' + query + '&LocationType=WIP')
                        .then(function(res) {
                            if(res.data.Success == true) {
                                if(res.data.Result.length > 0) {
                                    var result_array = [];
                                    for(var i=0;i<res.data.Result.length;i++) {
                                        result_array.push({value: res.data.Result[i]['GroupName'],GroupName: res.data.Result[i]['GroupName']});
                                    }
                                    return result_array;
                                } else {
                                    return [];
                                }
                            }
                            else {
                                console.log(res.data.Result);
                                return [];
                            }
                        });
                    } else {
                        return [];
                    }
                } else {
                    return [];
                }
            }
        };

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'recovery/includes/parts_sort_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetInputResults&Workflow=Parts Sort&workflow_id=20',
            success: function(data){
                if(data.Success) {
                    $scope.InputResults = data.Result;
                    if(data.Default) {
                        $scope.asset.input_id = data.Default;
                        $scope.DefaultInputID = data.Default;
                    }
                } else {
                    $scope.InputResults = [];
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
          url: host+'administration/includes/admin_extended_submit.php',
          dataType: 'json',
          type: 'post',
          data: 'ajax=GetSortCriteria',
          success: function(data){
              if(data.Success == true) {
                  $scope.SortCriteria = data.Result;
              } else {
                  $mdToast.show (
                      $mdToast.simple()
                      .content(data.Result)
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
                  );
              }
              initSessionTime(); //$scope.$apply();
          }, error : function (data) {
              $scope.data = data;
              initSessionTime(); $scope.$apply();
          }

        });

        $scope.GetCurrentTime = function(item) {
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCurrentTime',
                success: function(data){
                    if(data.Success) {
                        $scope.asset[item] = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        $scope.asset[item] = '';
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        jQuery.ajax({
            url: host+'audit/includes/audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilityStations&Workflow=Parts Sort&workflow_id=20',
            success: function(data){
                if(data.Success) {
                    $scope.Stations = data.Result;
                } else {
                    $scope.Stations = [];
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });


        /*jQuery.ajax({
            url: host+'recovery/includes/parts_sort_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetGroupStations',
            success: function(data) {
                if(data.Success) {
                    $scope.GroupStations = data.Result;
                } else {
                    $scope.GroupStations = [];
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }
        });*/

        $scope.GetCustomPalletDetails = function () {
          //console.log('asset = '+JSON.stringify($scope.asset));
          if(!$scope.asset.SiteID || $scope.asset.SiteID == ''){
            $mdToast.show (
                $mdToast.simple()
                    .content('Please select Station')
                    .action('OK')
                    .position('right')
                    .hideDelay(0)
                    .toastClass('md-toast-danger md-block')
            );
            return;
          }
            if(!$scope.asset.SortCriteriaID || $scope.asset.SortCriteriaID == ''){
              $mdToast.show (
                  $mdToast.simple()
                      .content('Please select Sort Validation Key')
                      .action('OK')
                      .position('right')
                      .hideDelay(0)
                      .toastClass('md-toast-danger md-block')
              );
              return;
            }
            if($scope.asset.BinName) {

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'recovery/includes/parts_sort_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetCustomPalletDetails&BinName='+$scope.asset.BinName+'&SiteID='+$scope.asset.SiteID,
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.asset.CustomPalletID = data.CustomPalletID;
                            $scope.FromBin = data.Result;
                            if(data.ID) {
                                $scope.asset.ID = data.ID;
                            }
                            setTimeout(function () {
                                $window.document.getElementById('SerialNumber').focus();
                            }, 100);
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.asset.CustomPalletID = '';
                            $scope.asset.ID = '';
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            } else {
                $scope.asset.CustomPalletID = '';
                $scope.asset.ID = '';
            }
        };

        $scope.GetToBinAssetsCount = function () {
          //console.log('asset = '+JSON.stringify($scope.asset));
            if($scope.asset.to_BinName) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'recovery/includes/parts_sort_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetBinDetails&BinName='+$scope.asset.to_BinName,
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.asset.AssetsCount = data.Result.AssetsCount;
                            $scope.asset.MaximumAssets = data.Result.MaximumAssets;
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            } else {
                $scope.asset.CustomPalletID = '';
                $scope.asset.ID = '';
            }
        };

        $scope.GetFromBinAssetsCount = function () {
          //console.log('asset = '+JSON.stringify($scope.asset));
            if($scope.asset.BinName) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'recovery/includes/parts_sort_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetBinDetails&BinName='+$scope.asset.BinName,
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.FromBin.AssetsCount = data.Result.AssetsCount;
                            $scope.FromBin.MaximumAssets = data.Result.MaximumAssets;
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            } else {

            }
        };

        $scope.GetMPNFromSerial = function () {
            $rootScope.$broadcast('preloader:active');
            $scope.asset.busy = true;
            jQuery.ajax({
                url: host+'recovery/includes/parts_sort_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetMPNFromSerial&SerialNumber='+$scope.asset.SerialNumber+'&CustomPalletID='+$scope.asset.CustomPalletID,
                success: function(data){
                    if(data.Success) {
                        $scope.asset.MPN = data.Result.UniversalModelNumber;
                        $scope.asset.parttypeid = data.Result.parttypeid;
                        $scope.GetCurrentTime('mpn_scan_time');
                        $scope.GetCurrentTime('evaluation_result_scan_time');
                        if(data.Result.AssetScanID){
                          $scope.asset.AssetScanID = data.Result.AssetScanID;
                        }                        
                        $scope.asset.idPallet = data.Result.idPallet;
                        $scope.asset.COOID = data.Result.COOID;
                        $scope.COOList = data.COOList;
                        $scope.ApplyBusinessRule();
                    } else {
                        $scope.asset.MPN = '';
                        $scope.asset.AssetScanID = '';
                        $scope.asset.idPallet = '';
                        $scope.asset.COOID = '';
                        $scope.COOList = [];
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                    }
                    $scope.asset.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.asset.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.ApplyBusinessRule = function () {
            if($scope.asset.input_id > 0) {
                $scope.asset.UniversalModelNumber = $scope.asset.MPN;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ApplyBusinessRule&workflow_id=20&'+$.param($scope.asset)+'&WorkStationGroupID='+$scope.asset.WorkStationGroupID,
                    success: function(data){
                        if(data.Success) {
                            if (data.ExactMPN) {
                                $scope.asset.MPN = data.ExactMPN;
                            }
                            if(data.MPN.part_spec_id) {                                
                                $scope.asset.part_spec_id = data.MPN.part_spec_id;
                            } else {
                                $scope.asset.part_spec_id = null;
                            }
                            if(data.NoMapping == '1') {
                                /*$mdToast.show (
                                    $mdToast.simple()
                                        .content('Bin is not configured in Sort Configuration, Please configure in Sort Configuration')
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-info md-block')
                                );*/

                                $scope.GetSameDispositionBin();
                            } else {
                                $scope.asset.to_disposition_id = data.Result.disposition_id;
                                $scope.asset.to_disposition = data.Result.disposition;
                                $scope.asset.rule_id = data.Result.rule_id;
                                if(data.CustomPalletID) {
                                    $scope.asset.to_CustomPalletID = data.CustomPalletID;
                                    $scope.asset.to_BinName = data.BinName;
                                    $scope.asset.sortconfigurationid = data.sortconfigurationid;
                                    $scope.asset.LocationID = data.LocationID;
                                    $scope.asset.LocationName = data.LocationName;
                                    $scope.asset.AssetsCount = data.AssetsCount;
                                    $scope.asset.MaximumAssets = data.MaximumAssets;
                                    $scope.GetCurrentTime('destination_bin_scan_time');
                                }else{
                                  $scope.asset.to_CustomPalletID = '';
                                  $scope.asset.to_BinName = '';
                                  $scope.asset.sortconfigurationid = '';
                                  $scope.asset.LocationID = '';
                                  $scope.asset.LocationName = '';
                                  $scope.asset.AssetsCount = '';
                                  $scope.asset.MaximumAssets = '';
                                  $scope.GetCurrentTime('destination_bin_scan_time');
                                }
                                $scope.asset.rule_description = data.Result.rule_description;
                                $scope.asset.rule_id_text  = data.Result.rule_id_text;
                                $scope.disposition_color = data.Result.color_code;
                                $window.document.getElementById('save_button').focus();
                            }
                        } else {
                            if (data.ExactMPN) {
                                $scope.asset.MPN = data.ExactMPN;
                            }
                            if(data.MPN.part_spec_id) {
                                $scope.asset.part_spec_id = data.MPN.part_spec_id;
                            } else {
                                $scope.asset.part_spec_id = null;
                            }
                            $scope.asset.to_disposition_id = '';
                            $scope.asset.to_disposition = '';
                            $scope.disposition_color = '';
                            $scope.asset.to_CustomPalletID = '';
                            $scope.asset.to_BinName = '';
                            $scope.asset.AssetsCount = '';
                            $scope.asset.MaximumAssets = '';
                            $scope.asset.rule_id = '';
                            $scope.asset.rule_description = '';
                            $scope.asset.rule_id_text  = '';
                            //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                            //if(data.NoRule == '1') {
                            if(true) {
                                /*$mdToast.show (
                                    $mdToast.simple()
                                        .content('No Rules Satisfied,Checking for Same Disposition Bin')
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-info md-block')
                                );*/
                                $scope.GetSameDispositionBin();
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }

                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };

        $scope.SortCancel = function () {
            $scope.asset.MPN = '';
            $scope.asset.AssemblyRecoveryRecordID = '';
            $scope.asset.idPallet = '';
            $scope.asset.SerialNumber = '';
            $scope.asset.to_disposition = '';
            $scope.disposition_color = '';
            $scope.asset.to_CustomPalletID = '';
            $scope.asset.to_BinName = '';
            $scope.asset.COOID = '';
            $scope.asset.LocationName = '';
            $scope.asset.rule_id_text = '';
            $scope.asset.rule_description = '';
            window.location = "#!/PartsSort";
        };

        $scope.SortPart = function () {
            $rootScope.$broadcast('preloader:active');
            $scope.asset.busy = true;
            jQuery.ajax({
                url: host+'recovery/includes/parts_sort_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=SortPart&'+$.param($scope.asset),
                success: function(data){
                    if(data.Success) {
                        $scope.asset.MPN = '';
                        $scope.asset.AssemblyRecoveryRecordID = '';
                        $scope.asset.idPallet = '';
                        $scope.asset.SerialNumber = '';
                        $scope.asset.to_disposition = '';
                        $scope.disposition_color = '';
                        $scope.asset.to_CustomPalletID = '';
                        $scope.asset.to_BinName = '';
                        $scope.asset.AssetsCount = '';
                        $scope.asset.MaximumAssets = '';
                        $scope.asset.COOID = '';
                        //$scope.asset.input_id = '';
                        $scope.asset.LocationName = '';
                        $scope.asset.rule_id_text = '';
                        $scope.asset.rule_description = '';
                        $window.document.getElementById('SerialNumber').focus();
                        $scope.GetCustomPalletDetails();
                        $scope.GetSortHistory();
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                    }
                    $scope.asset.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.asset.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });
        };


        $scope.focusNextField = function (id) {
            $window.document.getElementById(id).focus();
        };

        $scope.GetSortHistory = function () {
            if($scope.asset.CustomPalletID > 0) {

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'recovery/includes/parts_sort_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetSortHistory&CustomPalletID='+$scope.asset.CustomPalletID,
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.SortHistory = data.Result;
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.SortHistory = [];
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            } else {
                $scope.SortHistory = [];
            }
        };

        $scope.LockWorkStation = function () {
            if($scope.asset.SiteID > 0) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'recovery/includes/parts_sort_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=LockWorkStation&SiteID='+$scope.asset.SiteID,
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };

        $scope.SNChanged = function () {
            $scope.asset.MPN = '';
            $scope.asset.COOID = '';
            //$scope.asset.input_id = '';
            $scope.asset.LocationName = '';
            $scope.asset.to_disposition = '';
            $scope.disposition_color = '';
            $scope.asset.to_BinName = '';
            $scope.asset.rule_id_text = '';
            $scope.asset.rule_description = '';

        };

        $scope.MPNChanged = function () {
            $scope.asset.COOID = '';
            //$scope.asset.input_id = '';
            $scope.asset.LocationName = '';
            $scope.asset.to_disposition = '';
            $scope.disposition_color = '';
            $scope.asset.to_BinName = '';
            $scope.asset.rule_id_text = '';
            $scope.asset.rule_description = '';

        };

        $scope.SourceBinChanged = function () {
            $scope.asset.CustomPalletID = '';
            $scope.asset.SerialNumber = '';
            $scope.asset.MPN = '';
            $scope.asset.COOID = '';
            //$scope.asset.input_id = '';
            $scope.asset.LocationName = '';
            $scope.asset.to_disposition = '';
            $scope.disposition_color = '';
            $scope.asset.to_BinName = '';
            $scope.asset.rule_id_text = '';
            $scope.asset.rule_description = '';
            $scope.SortHistory = [];
        };

        $scope.GetSameDispositionBin = function () {

            // $mdToast.show (
            //     $mdToast.simple()
            //         .content('Getting Same Disposition BIN')
            //         .action('OK')
            //         .position('right')
            //         .hideDelay(0)
            //         .toastClass('md-toast-info md-block')
            // );

            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'recovery/includes/parts_sort_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetSameDispositionBin&'+$.param($scope.asset),
                success: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.asset.to_disposition_id = data.Result.disposition_id;
                        $scope.asset.to_disposition = data.Result.disposition;
                        $scope.asset.rule_id = data.Result.rule_id;
                        if(data.CustomPalletID) {
                            $scope.asset.to_CustomPalletID = data.CustomPalletID;
                            $scope.asset.to_BinName = data.BinName;
                            $scope.asset.sortconfigurationid = data.sortconfigurationid;
                            $scope.asset.LocationID = data.LocationID;
                            $scope.asset.LocationName = data.LocationName;
                            $scope.asset.AssetsCount = data.AssetsCount;
                            $scope.asset.MaximumAssets = data.MaximumAssets;
                            $scope.GetCurrentTime('destination_bin_scan_time');
                        }
                        $scope.asset.rule_description = data.Result.rule_description;
                        $scope.asset.rule_id_text  = data.Result.rule_id_text;
                        $scope.disposition_color = data.Result.color_code;
                        $window.document.getElementById('save_button').focus();
                    } else {
                        if (data.ExactMPN) {
                            $scope.asset.MPN = data.ExactMPN;
                        }
                        $scope.asset.to_disposition_id = '';
                        $scope.asset.to_disposition = '';
                        $scope.disposition_color = '';
                        $scope.asset.to_CustomPalletID = '';
                        $scope.asset.to_BinName = '';
                        $scope.asset.AssetsCount = '';
                        $scope.asset.MaximumAssets = '';
                        $scope.asset.rule_id = '';
                        $scope.asset.rule_description = '';
                        $scope.asset.rule_id_text  = '';
                        //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });

        };

    });

})();
