<?php
session_start();
include_once("../connection.php");
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();


$query= "select * from fleet_risk_serials_working where Completed = 0 Limit 100000";
$q = mysqli_query($connectionlink,$query);
if(mysqli_affected_rows($connectionlink) > 0) {
    $i = 0;
    while($row = mysqli_fetch_assoc($q)) {

        //Start Check If SerialNumber Exists								
        $query2 = "select ID from fleet_risk_serials where SerialNumber = '".mysqli_real_escape_string($connectionlink,$row['SerialNumber'])."'";
        $q2 = mysqli_query($connectionlink,$query2);
        if(mysqli_affected_rows($connectionlink) > 0) {
            $row2 = mysqli_fetch_assoc($q2);
            $ID = $row2['ID'];
        } else {//Create new manufacturer
            $ID = 0;
        }								
        //End Chedk If SerialNumber Exists

        if($ID > 0) { //ID Updating existing Serial									
            $query3 = "update fleet_risk_serials set fleet_risk = '".mysqli_real_escape_string($connectionlink,$row['fleet_risk'])."',updated_date = NOW(),recovery_project = '".mysqli_real_escape_string($connectionlink,$row['recovery_project'])."',special_handling = '".mysqli_real_escape_string($connectionlink,$row['special_handling'])."',internal_use = '".mysqli_real_escape_string($connectionlink,$row['internal_use'])."' where ID = '".$ID."'";
            $q3 = mysqli_query($connectionlink,$query3);
            if(mysqli_error($connectionlink)) {
                $error_message = $error_message . 'Error at Row '.$a.'  '.mysqli_error($connectionlink).'\n';
            } else {
                $updated_records = $updated_records + 1;
            }
        } else {//Create new MPN
            $query1 = "insert into fleet_risk_serials (SerialNumber,fleet_risk,created_date,recovery_project,special_handling,internal_use) values ('".mysqli_real_escape_string($connectionlink,$row['SerialNumber'])."','".mysqli_real_escape_string($connectionlink,$row['fleet_risk'])."',NOW(),'".mysqli_real_escape_string($connectionlink,$row['recovery_project'])."','".mysqli_real_escape_string($connectionlink,$row['special_handling'])."','".mysqli_real_escape_string($connectionlink,$row['internal_use'])."')";
            $q1 = mysqli_query($connectionlink,$query1);
            if(mysqli_error($connectionlink)) {
                $error_message = $error_message . 'Error at Row '.$a.'  '.mysqli_error($connectionlink).'\n';
            } else {
                $new_records = $new_records + 1;
            }
        }

        $query4 = "update fleet_risk_serials_working set Completed = 1 where ID = '".$row['ID']."'";
        $q4 = mysqli_query($connectionlink,$query4);
        if(mysqli_error($connectionlink)) {
            $error_message = $error_message . 'Error at Row '.$a.'  '.mysqli_error($connectionlink).'\n';
        }
        $i = $i + 1;
    }
    echo $error_message;
    echo $i." Records Completed";
} else {
    echo "No Records";
}
?>