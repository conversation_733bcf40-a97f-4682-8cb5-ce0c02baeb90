<?php
session_start();
include_once("../../config.php");
$dateformat = DATEFORMAT;
$data = $_SESSION['idPallet'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "ExportTrackRecords.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
//$datatoday = array('Generated Date',$today);

$SerialNumber = $_GET['SerialNumber'];
$ID = $_GET['ID'];
$Type = $_GET['Type'];

$header = array('Serial Number','Container ID','MPN','Part Type','Disposition','Recovery Type');
$datahead = array('Tracking Serials');
$rows = array();

if($Type == 'Load') {
	$query0 = "select A.idPallet,A.idPallet as SerialNumber,A.idPallet from pallets A 		
	where A.LoadId = '".mysqli_real_escape_string($connectionlink,$ID)."' ";	
	$q0 = mysqli_query($connectionlink,$query0);

	if(mysqli_affected_rows($connectionlink) > 0) {
		while($row0 = mysqli_fetch_assoc($q0)) {
			$row  = array($row0['SerialNumber'],$row0['idPallet'],'','Container','','Container');
			$rows[] = $row;	
			
			//Strat get from asset table
			$query1 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
			LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
			LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
			LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
			where A.idPallet = '".$row0['idPallet']."' and (r.Recoverytype = 'Container' or isnull(A.Recoverytypeid))";
			$q1 = mysqli_query($connectionlink,$query1);

			if(mysqli_affected_rows($connectionlink) > 0) {
				while($row1 = mysqli_fetch_assoc($q1)) {
					$row10  = array($row1['SerialNumber'],$row1['idPallet'],$row1['UniversalModelNumber'],$row1['parttype'],$row1['disposition'],$row1['Recoverytype']);
					$rows[] = $row10;
					
					//Start get next level
					$query12 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
					LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.TopLevelSerial = '".$row1['SerialNumber']."'";
					$q12 = mysqli_query($connectionlink,$query12);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row12 = mysqli_fetch_assoc($q12)) {
							$row102  = array($row12['SerialNumber'],$row12['idPallet'],$row12['UniversalModelNumber'],$row12['parttype'],$row12['disposition'],$row12['Recoverytype']);
							$rows[] = $row102;	

							

							//Start get next level
							$query212 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
							LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.TopLevelSerial = '".$row12['SerialNumber']."'";
							$q212 = mysqli_query($connectionlink,$query212);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row212 = mysqli_fetch_assoc($q212)) {
									$row10212  = array($row212['SerialNumber'],$row212['idPallet'],$row212['UniversalModelNumber'],$row212['parttype'],$row212['disposition'],$row212['Recoverytype']);
									$rows[] = $row10212;	
								}
							}


							$query312 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.ServerSerialNumber = '".$row12['SerialNumber']."'";
							$q312 = mysqli_query($connectionlink,$query312);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row312 = mysqli_fetch_assoc($q312)) {
									$row11012  = array($row312['SerialNumber'],$row312['idPallet'],$row312['UniversalModelNumber'],$row312['parttype'],$row312['disposition'],'Assembly');
									$rows[] = $row11012;	
								}
							}





						}
					}
					
				}
			}

			//Start get from speed server recovery table
			$query2 = "select A.ServerID,A.ServerSerialNumber as SerialNumber,A.idPallet,A.MPN as UniversalModelNumber,D.disposition,pt.parttype from speed_server_recovery A
			LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
			LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
			where A.idPallet = '".mysqli_real_escape_string($connectionlink,$row0['idPallet'])."' ";
			$q2 = mysqli_query($connectionlink,$query2);

			if(mysqli_affected_rows($connectionlink) > 0) {
				while($row2 = mysqli_fetch_assoc($q2)) {
					$row110  = array($row2['SerialNumber'],$row2['idPallet'],$row2['UniversalModelNumber'],$row2['parttype'],$row2['disposition'],'Rack');
					$rows[] = $row110;	

					//Start get next level
					$query21 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
					LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.TopLevelSerial = '".$row2['SerialNumber']."'";
					$q21 = mysqli_query($connectionlink,$query21);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row21 = mysqli_fetch_assoc($q21)) {
							$row1021  = array($row21['SerialNumber'],$row21['idPallet'],$row21['UniversalModelNumber'],$row21['parttype'],$row21['disposition'],$row21['Recoverytype']);
							$rows[] = $row1021;	



							//Start get next level
							$query2122 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
							LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.TopLevelSerial = '".$row21['SerialNumber']."'";
							$q2122 = mysqli_query($connectionlink,$query2122);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row2122 = mysqli_fetch_assoc($q2122)) {
									$row102122  = array($row2122['SerialNumber'],$row2122['idPallet'],$row2122['UniversalModelNumber'],$row2122['parttype'],$row2122['disposition'],$row2122['Recoverytype']);
									$rows[] = $row102122;	
								}
							}


							$query3122 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.ServerSerialNumber = '".$row21['SerialNumber']."'";
							$q3122 = mysqli_query($connectionlink,$query3122);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row3122 = mysqli_fetch_assoc($q3122)) {
									$row110122  = array($row3122['SerialNumber'],$row3122['idPallet'],$row3122['UniversalModelNumber'],$row3122['parttype'],$row3122['disposition'],'Assembly');
									$rows[] = $row110122;	
								}
							}


						}
					}


					$query31 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.ServerSerialNumber = '".$row2['SerialNumber']."'";
					$q31 = mysqli_query($connectionlink,$query31);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row31 = mysqli_fetch_assoc($q31)) {
							$row1101  = array($row31['SerialNumber'],$row31['idPallet'],$row31['UniversalModelNumber'],$row31['parttype'],$row31['disposition'],'Assembly');
							$rows[] = $row1101;	


							//Start get next level
							$query21221 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
							LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.TopLevelSerial = '".$row31['SerialNumber']."'";
							$q21221 = mysqli_query($connectionlink,$query21221);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row21221 = mysqli_fetch_assoc($q21221)) {
									$row1021221  = array($row21221['SerialNumber'],$row21221['idPallet'],$row21221['UniversalModelNumber'],$row21221['parttype'],$row21221['disposition'],$row21221['Recoverytype']);
									$rows[] = $row1021221;	
								}
							}


							$query31221 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.ServerSerialNumber = '".$row31['SerialNumber']."'";
							$q31221 = mysqli_query($connectionlink,$query31221);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row31221 = mysqli_fetch_assoc($q31221)) {
									$row1101221  = array($row31221['SerialNumber'],$row31221['idPallet'],$row31221['UniversalModelNumber'],$row31221['parttype'],$row31221['disposition'],'Assembly');
									$rows[] = $row1101221;	
								}
							}
						}
					}

				}
			}
		}
	}
}


if($Type == 'Container') {
	$query0 = "select A.idPallet,A.idPallet as SerialNumber,A.idPallet from pallets A 		
	where A.idPallet = '".mysqli_real_escape_string($connectionlink,$ID)."' ";	
	$q0 = mysqli_query($connectionlink,$query0);

	if(mysqli_affected_rows($connectionlink) > 0) {
		while($row0 = mysqli_fetch_assoc($q0)) {
			$row  = array($row0['SerialNumber'],$row0['idPallet'],'','Container','','Container');
			$rows[] = $row;	
			
			//Strat get from asset table
			$query1 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
			LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
			LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
			LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
			where A.idPallet = '".$row0['idPallet']."' and (r.Recoverytype = 'Container' or isnull(A.Recoverytypeid))";
			$q1 = mysqli_query($connectionlink,$query1);

			if(mysqli_affected_rows($connectionlink) > 0) {
				while($row1 = mysqli_fetch_assoc($q1)) {
					$row10  = array($row1['SerialNumber'],$row1['idPallet'],$row1['UniversalModelNumber'],$row1['parttype'],$row1['disposition'],$row1['Recoverytype']);
					$rows[] = $row10;
					
					//Start get next level
					$query12 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
					LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.TopLevelSerial = '".$row1['SerialNumber']."'";
					$q12 = mysqli_query($connectionlink,$query12);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row12 = mysqli_fetch_assoc($q12)) {
							$row102  = array($row12['SerialNumber'],$row12['idPallet'],$row12['UniversalModelNumber'],$row12['parttype'],$row12['disposition'],$row12['Recoverytype']);
							$rows[] = $row102;	

							

							//Start get next level
							$query212 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
							LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.TopLevelSerial = '".$row12['SerialNumber']."'";
							$q212 = mysqli_query($connectionlink,$query212);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row212 = mysqli_fetch_assoc($q212)) {
									$row10212  = array($row212['SerialNumber'],$row212['idPallet'],$row212['UniversalModelNumber'],$row212['parttype'],$row212['disposition'],$row212['Recoverytype']);
									$rows[] = $row10212;	
								}
							}


							$query312 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.ServerSerialNumber = '".$row12['SerialNumber']."'";
							$q312 = mysqli_query($connectionlink,$query312);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row312 = mysqli_fetch_assoc($q312)) {
									$row11012  = array($row312['SerialNumber'],$row312['idPallet'],$row312['UniversalModelNumber'],$row312['parttype'],$row312['disposition'],'Assembly');
									$rows[] = $row11012;	
								}
							}





						}
					}
					
				}
			}

			//Start get from speed server recovery table
			$query2 = "select A.ServerID,A.ServerSerialNumber as SerialNumber,A.idPallet,A.MPN as UniversalModelNumber,D.disposition,pt.parttype from speed_server_recovery A
			LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
			LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
			where A.idPallet = '".mysqli_real_escape_string($connectionlink,$row0['idPallet'])."' ";
			$q2 = mysqli_query($connectionlink,$query2);

			if(mysqli_affected_rows($connectionlink) > 0) {
				while($row2 = mysqli_fetch_assoc($q2)) {
					$row110  = array($row2['SerialNumber'],$row2['idPallet'],$row2['UniversalModelNumber'],$row2['parttype'],$row2['disposition'],'Rack');
					$rows[] = $row110;	

					//Start get next level
					$query21 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
					LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.TopLevelSerial = '".$row2['SerialNumber']."'";
					$q21 = mysqli_query($connectionlink,$query21);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row21 = mysqli_fetch_assoc($q21)) {
							$row1021  = array($row21['SerialNumber'],$row21['idPallet'],$row21['UniversalModelNumber'],$row21['parttype'],$row21['disposition'],$row21['Recoverytype']);
							$rows[] = $row1021;	



							//Start get next level
							$query2122 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
							LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.TopLevelSerial = '".$row21['SerialNumber']."'";
							$q2122 = mysqli_query($connectionlink,$query2122);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row2122 = mysqli_fetch_assoc($q2122)) {
									$row102122  = array($row2122['SerialNumber'],$row2122['idPallet'],$row2122['UniversalModelNumber'],$row2122['parttype'],$row2122['disposition'],$row2122['Recoverytype']);
									$rows[] = $row102122;	
								}
							}


							$query3122 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.ServerSerialNumber = '".$row21['SerialNumber']."'";
							$q3122 = mysqli_query($connectionlink,$query3122);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row3122 = mysqli_fetch_assoc($q3122)) {
									$row110122  = array($row3122['SerialNumber'],$row3122['idPallet'],$row3122['UniversalModelNumber'],$row3122['parttype'],$row3122['disposition'],'Assembly');
									$rows[] = $row110122;	
								}
							}


						}
					}


					$query31 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.ServerSerialNumber = '".$row2['SerialNumber']."'";
					$q31 = mysqli_query($connectionlink,$query31);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row31 = mysqli_fetch_assoc($q31)) {
							$row1101  = array($row31['SerialNumber'],$row31['idPallet'],$row31['UniversalModelNumber'],$row31['parttype'],$row31['disposition'],'Assembly');
							$rows[] = $row1101;	


							//Start get next level
							$query21221 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
							LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.TopLevelSerial = '".$row31['SerialNumber']."'";
							$q21221 = mysqli_query($connectionlink,$query21221);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row21221 = mysqli_fetch_assoc($q21221)) {
									$row1021221  = array($row21221['SerialNumber'],$row21221['idPallet'],$row21221['UniversalModelNumber'],$row21221['parttype'],$row21221['disposition'],$row21221['Recoverytype']);
									$rows[] = $row1021221;	
								}
							}


							$query31221 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.ServerSerialNumber = '".$row31['SerialNumber']."'";
							$q31221 = mysqli_query($connectionlink,$query31221);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row31221 = mysqli_fetch_assoc($q31221)) {
									$row1101221  = array($row31221['SerialNumber'],$row31221['idPallet'],$row31221['UniversalModelNumber'],$row31221['parttype'],$row31221['disposition'],'Assembly');
									$rows[] = $row1101221;	
								}
							}
						}
					}

				}
			}
		}
	}
}



if($Type == 'Asset') {

	//Strat get from asset table
	$query1 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
	LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
	LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
	LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
	where A.SerialNumber = '".$SerialNumber."'";
	$q1 = mysqli_query($connectionlink,$query1);

	if(mysqli_affected_rows($connectionlink) > 0) {
		while($row1 = mysqli_fetch_assoc($q1)) {
			$row10  = array($row1['SerialNumber'],$row1['idPallet'],$row1['UniversalModelNumber'],$row1['parttype'],$row1['disposition'],$row1['Recoverytype']);
			$rows[] = $row10;
			
			//Start get next level
			$query12 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
			LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
			LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
			LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
			where A.TopLevelSerial = '".$row1['SerialNumber']."'";
			$q12 = mysqli_query($connectionlink,$query12);
			if(mysqli_affected_rows($connectionlink) > 0) {
				while($row12 = mysqli_fetch_assoc($q12)) {
					$row102  = array($row12['SerialNumber'],$row12['idPallet'],$row12['UniversalModelNumber'],$row12['parttype'],$row12['disposition'],$row12['Recoverytype']);
					$rows[] = $row102;	

					

					//Start get next level
					$query212 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
					LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.TopLevelSerial = '".$row12['SerialNumber']."'";
					$q212 = mysqli_query($connectionlink,$query212);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row212 = mysqli_fetch_assoc($q212)) {
							$row10212  = array($row212['SerialNumber'],$row212['idPallet'],$row212['UniversalModelNumber'],$row212['parttype'],$row212['disposition'],$row212['Recoverytype']);
							$rows[] = $row10212;	
						}
					}


					$query312 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.ServerSerialNumber = '".$row12['SerialNumber']."'";
					$q312 = mysqli_query($connectionlink,$query312);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row312 = mysqli_fetch_assoc($q312)) {
							$row11012  = array($row312['SerialNumber'],$row312['idPallet'],$row312['UniversalModelNumber'],$row312['parttype'],$row312['disposition'],'Assembly');
							$rows[] = $row11012;	
						}
					}





				}
			}
			
		}
	}
	
	

	$query312 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
	LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
	LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
	where A.ServerSerialNumber = '".$SerialNumber."'";
	$q312 = mysqli_query($connectionlink,$query312);
	if(mysqli_affected_rows($connectionlink) > 0) {
		while($row312 = mysqli_fetch_assoc($q312)) {
			$row11012  = array($row312['SerialNumber'],$row312['idPallet'],$row312['UniversalModelNumber'],$row312['parttype'],$row312['disposition'],'Assembly');
			$rows[] = $row11012;	


			$query12 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
			LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
			LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
			LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
			where A.TopLevelSerial = '".$row312['SerialNumber']."'";
			$q12 = mysqli_query($connectionlink,$query12);
			if(mysqli_affected_rows($connectionlink) > 0) {
				while($row12 = mysqli_fetch_assoc($q12)) {
					$row102  = array($row12['SerialNumber'],$row12['idPallet'],$row12['UniversalModelNumber'],$row12['parttype'],$row12['disposition'],$row12['Recoverytype']);
					$rows[] = $row102;
				}
			}
		}
	}
}



if($Type == 'Server') {	
	$query0 = "select A.ServerID,A.ServerSerialNumber as SerialNumber,A.idPallet,A.MPN as UniversalModelNumber,D.disposition,pt.parttype from speed_server_recovery A
	LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
	LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
	where A.ServerSerialNumber = '".mysqli_real_escape_string($connectionlink,$SerialNumber)."' ";
	$q0 = mysqli_query($connectionlink,$query0);

	if(mysqli_affected_rows($connectionlink) > 0) {
		while($row0 = mysqli_fetch_assoc($q0)) {
			$row  = array($row0['SerialNumber'],$row0['idPallet'],$row0['UniversalModelNumber'],$row0['parttype'],$row0['disposition'],'Rack');
			$rows[] = $row;	
			
			//Strat get from asset table
			$query1 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
			LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
			LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
			LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
			where A.TopLevelSerial = '".$row0['SerialNumber']."' ";
			$q1 = mysqli_query($connectionlink,$query1);

			if(mysqli_affected_rows($connectionlink) > 0) {
				while($row1 = mysqli_fetch_assoc($q1)) {
					$row10  = array($row1['SerialNumber'],$row1['idPallet'],$row1['UniversalModelNumber'],$row1['parttype'],$row1['disposition'],$row1['Recoverytype']);
					$rows[] = $row10;
					
					//Start get next level
					$query12 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
					LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.TopLevelSerial = '".$row1['SerialNumber']."'";
					$q12 = mysqli_query($connectionlink,$query12);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row12 = mysqli_fetch_assoc($q12)) {
							$row102  = array($row12['SerialNumber'],$row12['idPallet'],$row12['UniversalModelNumber'],$row12['parttype'],$row12['disposition'],$row12['Recoverytype']);
							$rows[] = $row102;	

							

							//Start get next level
							$query212 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
							LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.TopLevelSerial = '".$row12['SerialNumber']."'";
							$q212 = mysqli_query($connectionlink,$query212);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row212 = mysqli_fetch_assoc($q212)) {
									$row10212  = array($row212['SerialNumber'],$row212['idPallet'],$row212['UniversalModelNumber'],$row212['parttype'],$row212['disposition'],$row212['Recoverytype']);
									$rows[] = $row10212;	
								}
							}


							$query312 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.ServerSerialNumber = '".$row12['SerialNumber']."'";
							$q312 = mysqli_query($connectionlink,$query312);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row312 = mysqli_fetch_assoc($q312)) {
									$row11012  = array($row312['SerialNumber'],$row312['idPallet'],$row312['UniversalModelNumber'],$row312['parttype'],$row312['disposition'],'Assembly');
									$rows[] = $row11012;	
								}
							}





						}
					}


					$query31221 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.ServerSerialNumber = '".$row1['SerialNumber']."'";
					$q31221 = mysqli_query($connectionlink,$query31221);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row31221 = mysqli_fetch_assoc($q31221)) {
							$row1101221  = array($row31221['SerialNumber'],$row31221['idPallet'],$row31221['UniversalModelNumber'],$row31221['parttype'],$row31221['disposition'],'Assembly');
							$rows[] = $row1101221;	
						}
					}
					
				}
			}
			


			$query31 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
			LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
			LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
			where A.ServerSerialNumber = '".$row0['SerialNumber']."'";
			$q31 = mysqli_query($connectionlink,$query31);
			if(mysqli_affected_rows($connectionlink) > 0) {
				while($row31 = mysqli_fetch_assoc($q31)) {
					$row1101  = array($row31['SerialNumber'],$row31['idPallet'],$row31['UniversalModelNumber'],$row31['parttype'],$row31['disposition'],'Assembly');
					$rows[] = $row1101;	


					//Start get next level
					$query21221 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
					LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.TopLevelSerial = '".$row31['SerialNumber']."'";
					$q21221 = mysqli_query($connectionlink,$query21221);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row21221 = mysqli_fetch_assoc($q21221)) {
							$row1021221  = array($row21221['SerialNumber'],$row21221['idPallet'],$row21221['UniversalModelNumber'],$row21221['parttype'],$row21221['disposition'],$row21221['Recoverytype']);
							$rows[] = $row1021221;	
						}
					}


					$query31221 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.ServerSerialNumber = '".$row31['SerialNumber']."'";
					$q31221 = mysqli_query($connectionlink,$query31221);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row31221 = mysqli_fetch_assoc($q31221)) {
							$row1101221  = array($row31221['SerialNumber'],$row31221['idPallet'],$row31221['UniversalModelNumber'],$row31221['parttype'],$row31221['disposition'],'Assembly');
							$rows[] = $row1101221;	
						}
					}
				}
			}
			

		}
	}
}


if($Type == 'Media') {	
	$query0 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
	LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
	LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
	where A.MediaSerialNumber = '".mysqli_real_escape_string($connectionlink,$SerialNumber)."'";
	$q0 = mysqli_query($connectionlink,$query0);

	if(mysqli_affected_rows($connectionlink) > 0) {
		while($row0 = mysqli_fetch_assoc($q0)) {
			$row  = array($row0['SerialNumber'],$row0['idPallet'],$row0['UniversalModelNumber'],$row0['parttype'],$row0['disposition'],'Assembly');
			$rows[] = $row;	
			
			//Strat get from asset table
			$query1 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
			LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
			LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
			LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
			where A.TopLevelSerial = '".$row0['SerialNumber']."' ";
			$q1 = mysqli_query($connectionlink,$query1);

			if(mysqli_affected_rows($connectionlink) > 0) {
				while($row1 = mysqli_fetch_assoc($q1)) {
					$row10  = array($row1['SerialNumber'],$row1['idPallet'],$row1['UniversalModelNumber'],$row1['parttype'],$row1['disposition'],$row1['Recoverytype']);
					$rows[] = $row10;
					
					//Start get next level
					$query12 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
					LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.TopLevelSerial = '".$row1['SerialNumber']."'";
					$q12 = mysqli_query($connectionlink,$query12);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row12 = mysqli_fetch_assoc($q12)) {
							$row102  = array($row12['SerialNumber'],$row12['idPallet'],$row12['UniversalModelNumber'],$row12['parttype'],$row12['disposition'],$row12['Recoverytype']);
							$rows[] = $row102;	

							

							//Start get next level
							$query212 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
							LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.TopLevelSerial = '".$row12['SerialNumber']."'";
							$q212 = mysqli_query($connectionlink,$query212);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row212 = mysqli_fetch_assoc($q212)) {
									$row10212  = array($row212['SerialNumber'],$row212['idPallet'],$row212['UniversalModelNumber'],$row212['parttype'],$row212['disposition'],$row212['Recoverytype']);
									$rows[] = $row10212;	
								}
							}


							$query312 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
							LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
							LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
							where A.ServerSerialNumber = '".$row12['SerialNumber']."'";
							$q312 = mysqli_query($connectionlink,$query312);
							if(mysqli_affected_rows($connectionlink) > 0) {
								while($row312 = mysqli_fetch_assoc($q312)) {
									$row11012  = array($row312['SerialNumber'],$row312['idPallet'],$row312['UniversalModelNumber'],$row312['parttype'],$row312['disposition'],'Assembly');
									$rows[] = $row11012;	
								}
							}





						}
					}


					$query31221 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.ServerSerialNumber = '".$row1['SerialNumber']."'";
					$q31221 = mysqli_query($connectionlink,$query31221);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row31221 = mysqli_fetch_assoc($q31221)) {
							$row1101221  = array($row31221['SerialNumber'],$row31221['idPallet'],$row31221['UniversalModelNumber'],$row31221['parttype'],$row31221['disposition'],'Assembly');
							$rows[] = $row1101221;	
						}
					}
					
				}
			}
			


			$query31 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
			LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
			LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
			where A.ServerSerialNumber = '".$row0['SerialNumber']."'";
			$q31 = mysqli_query($connectionlink,$query31);
			if(mysqli_affected_rows($connectionlink) > 0) {
				while($row31 = mysqli_fetch_assoc($q31)) {
					$row1101  = array($row31['SerialNumber'],$row31['idPallet'],$row31['UniversalModelNumber'],$row31['parttype'],$row31['disposition'],'Assembly');
					$rows[] = $row1101;	


					//Start get next level
					$query21221 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
					LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.TopLevelSerial = '".$row31['SerialNumber']."'";
					$q21221 = mysqli_query($connectionlink,$query21221);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row21221 = mysqli_fetch_assoc($q21221)) {
							$row1021221  = array($row21221['SerialNumber'],$row21221['idPallet'],$row21221['UniversalModelNumber'],$row21221['parttype'],$row21221['disposition'],$row21221['Recoverytype']);
							$rows[] = $row1021221;	
						}
					}


					$query31221 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					where A.ServerSerialNumber = '".$row31['SerialNumber']."'";
					$q31221 = mysqli_query($connectionlink,$query31221);
					if(mysqli_affected_rows($connectionlink) > 0) {
						while($row31221 = mysqli_fetch_assoc($q31221)) {
							$row1101221  = array($row31221['SerialNumber'],$row31221['idPallet'],$row31221['UniversalModelNumber'],$row31221['parttype'],$row31221['disposition'],'Assembly');
							$rows[] = $row1101221;	
						}
					}
				}
			}
			

		}
	}
}


// if(count($rows) > 0) {
// 	for($i=0;$i<count($rows);$i++) {
// 		$query1 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
// 		LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
// 		LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
// 		LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
// 		where A.TopLevelSerial = '".$rows[$i]['SerialNumber']."'";
// 		$q1 = mysqli_query($connectionlink,$query1);
// 		if(mysqli_affected_rows($connectionlink) > 0) {
// 			while($row1 = mysqli_fetch_assoc($q1)) {
// 				$row10  = array($row1['SerialNumber'],$row1['idPallet'],$row1['UniversalModelNumber'],$row1['parttype'],$row1['disposition'],$row1['Recoverytype']);
// 				$rows[] = $row10;	
// 			}
// 		}

// 		$query2 = "select A.ServerID,A.ServerSerialNumber as SerialNumber,A.idPallet,A.MPN as UniversalModelNumber,D.disposition,pt.parttype from speed_server_recovery A
// 		LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
// 		LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
// 		where A.idPallet = '".$rows[$i]['idPallet']."'";
		
// 		$q2 = mysqli_query($connectionlink,$query2);
// 		if(mysqli_affected_rows($connectionlink) > 0) {
// 			while($row2 = mysqli_fetch_assoc($q2)) {
// 				$row110  = array($row2['SerialNumber'],$row2['idPallet'],$row2['UniversalModelNumber'],$row2['parttype'],$row2['disposition'],'Rack');
// 				$rows[] = $row110;	
// 			}
// 		}


// 		$query3 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
// 		LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
// 		LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
// 		where A.ServerSerialNumber = '".$rows[$i]['SerialNumber']."'";
// 		$q3 = mysqli_query($connectionlink,$query3);
// 		if(mysqli_affected_rows($connectionlink) > 0) {
// 			while($row3 = mysqli_fetch_assoc($q3)) {
// 				$row110  = array($row3['SerialNumber'],$row3['idPallet'],$row3['UniversalModelNumber'],$row3['parttype'],$row3['disposition'],'Assembly');
// 				$rows[] = $row110;	
// 			}
// 		}

// 	}
// }

/*$query1 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
left join pallets p on A.idPallet = p.idPallet where A.idPallet = '".mysqli_real_escape_string($connectionlink,$data)."' ";
$q1 = mysqli_query($connectionlink,$query1);
if(mysqli_affected_rows($connectionlink) > 0) {
	while($row1 = mysqli_fetch_assoc($q1)) {
		$row2  = array($row1['SerialNumber'],$row1['idPallet'],$row1['UniversalModelNumber'],$row1['parttype'],$row1['disposition'],$row1['Recoverytype']);
		$rows[] = $row2;		
	}
}

$query2 = "select A.ServerID,A.ServerSerialNumber as SerialNumber,A.idPallet,A.MPN as UniversalModelNumber,D.disposition,pt.parttype from speed_server_recovery A
	LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
	LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
	where A.idPallet = '".mysqli_real_escape_string($connectionlink,$data)."' ";
$q2 = mysqli_query($connectionlink,$query2);
if(mysqli_affected_rows($connectionlink) > 0) {
	while($row3 = mysqli_fetch_assoc($q2)) {
		$row4  = array($row3['SerialNumber'],$row3['idPallet'],$row3['UniversalModelNumber'],$row3['parttype'],$row3['disposition'],'Rack');
		$rows[] = $row4;
	}
}


$query2 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
				LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
				LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
				where A.idPallet = '".mysqli_real_escape_string($connectionlink,$data)."' ";
$q2 = mysqli_query($connectionlink,$query2);
if(mysqli_affected_rows($connectionlink) > 0) {
	while($row3 = mysqli_fetch_assoc($q2)) {
		$row4  = array($row3['SerialNumber'],$row3['idPallet'],$row3['UniversalModelNumber'],$row3['parttype'],$row3['disposition'],'Assembly');
		$rows[] = $row4;
	}
}*/

//$datatoday = array('Generated Date',$query1);

$sheet_name = 'Tracking Serials';
$style1 = array( ['font-style'=>'bold'],['font-style'=>''],['valign'=>'left']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->markMergedCell($sheet_name, $start_row = 0, $start_col = 0, $end_row = 2, $end_col = 5);
$writer->writeSheetRow($sheet_name, $datahead, $col_options = ['font-style'=>'bold','font-size'=>20,'halign'=>'center','valign'=>'center']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
$writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom']);
$writer->writeToStdOut();
//exit(0);
?>
