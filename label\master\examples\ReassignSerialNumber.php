<?php
session_start();
$id = $_GET['id'];
//$type = $_GET['parttype'];
// Include the main TCPDF library (search for installation path).
require_once('tcpdf_include.php');
// create new PDF document
$pdf = new TCPDF('L', 'mm', array('60','80'), true, 'UTF-8', false);;
//$pdf = new TCPDF('P', 'mm', array('60','127'), true, 'UTF-8', false);
//$pdf = new TCPDF('P', 'mm', array('101.6','76.2'), true, 'UTF-8', false);
//$pdf = new TCPDF('P', 'mm', array('101.6','152.4'), true, 'UTF-8', false);
// set document information
$pdf->SetCreator(PDF_CREATOR);
$pdf->SetAuthor('Eviridis');
$pdf->SetTitle('Receive Serial');
$pdf->SetSubject('Receive Serial');
$pdf->SetKeywords('TCPDF, PDF, example, test, guide');
// remove default header/footer
$pdf->setPrintHeader(false);
$pdf->setPrintFooter(false);
// set default monospaced font
$pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
// set margins
$pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
$pdf->setTopMargin(0);
$pdf->SetRightMargin(0);
$pdf->setHeaderMargin(0);
$pdf->SetFooterMargin(0);
// set auto page breaks
$pdf->SetAutoPageBreak(TRUE, 0);
// set image scale factor
$pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
// set some language-dependent strings (optional)
if (@file_exists(dirname(__FILE__).'/lang/eng.php')) {
    require_once(dirname(__FILE__).'/lang/eng.php');
    $pdf->setLanguageArray($l);
}
// ---------------------------------------------------------
// set font
//$pdf->SetFont('helvetica', '', 11);
//$pdf->SetFont("helvetica", "B", 13);
$pdf->SetFont("helvetica", "B", 10);
include_once("../../../connection.php");
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
include_once("../../config.php");
$dateformat = DATEFORMAT;

$sql = "select SerialNumber,UniversalModelNumber as MPN from asset where SerialNumber = '".mysqli_real_escape_string($connectionlink,$id)."'";
	$query = mysqli_query($connectionlink,$sql);

if(mysqli_affected_rows($connectionlink) == 0) {
  		$sql = "select ServerSerialNumber as SerialNumber,MPN from speed_server_recovery where ServerSerialNumber = '".mysqli_real_escape_string($connectionlink,$id)."'";
		$query = mysqli_query($connectionlink,$sql);

	if(mysqli_affected_rows($connectionlink) == 0) {
	  				
		$sql = "select MediaSerialNumber as SerialNumber,MediaMPN as MPN from speed_media_recovery where MediaSerialNumber = '".mysqli_real_escape_string($connectionlink,$id)."'";
		$query = mysqli_query($connectionlink,$sql);
	}
}
if(mysqli_error($connectionlink))
{
	echo mysqli_error($connectionlink);
}
$row = mysqli_fetch_assoc($query);

$SerialNumber = $row['SerialNumber'];
$SerialNumber_1 = "SerialNumber :".$SerialNumber;

$MPN = $row['MPN'];
$MPN_1 = "MPN :".$MPN;

// add a page
$pdf->AddPage();


		if(1) {			
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,$SerialNumber_1,0,1,'L',0,0,true,'','T');		
			$barcode = ($pdf->write1DBarcode($SerialNumber, 'C128', '15', '', 38, 12, '', $style, 'C'));	
		}

    	if($type != 'CPU') {			
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,'',0,1,'L',0,0,true,'','T');
			$pdf->setX(3);
			$pdf->Cell(0 , 0,$MPN_1,0,1,'L',0,0,true,'','T');		
			$barcode = ($pdf->write1DBarcode($MPN, 'C128', '15', '', 38, 12, '', $style, 'C'));	
		}
	
// ---------------------------------------------------------
//Close and output PDF document
$pdf->Output('ReassignSerialNumber.pdf', 'I');
//============================================================+
// END OF FILE
//============================================================+