<?php
session_start();
include_once("../../config.php");
$data = $_SESSION['ExportContainerListxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
//ini_set('display_errors', 0);
//ini_set('log_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date', $today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "ContainerList.xlsx";
header('Content-disposition: attachment; filename="' . XLSXWriter::sanitize_filename($filename) . '"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$connectionlink1 = Connection::DBConnect();
$datatoday = array('Generated Date', $today);
$datahead = array('Container List');

$sqlrejectcount = "select LoadId from pallets where LoadId = '".$data['LoadId']."' ";
$queryrejectcount = mysqli_query($connectionlink,$sqlrejectcount);
$rowrejectcount = mysqli_fetch_assoc($queryrejectcount);
$rejectcreated = array('Ticket ID',$rowrejectcount['LoadId']);


$sqlpasscount = "select p.*,f.FacilityName from pallets p left join facility f on f.FacilityID  = p.PalletFacilityID where LoadId = '".$data['LoadId']."' ";
$querypasscount = mysqli_query($connectionlink,$sqlpasscount);
$rowpasscount = mysqli_fetch_assoc($querypasscount);
$passcreated = array('Facility',$rowpasscount['FacilityName']);

$header = array('Container ID','Source','Container Type','Weight','Customer ID','Material Type','Source Type','Seal 1','Seal 2','Seal 3','Seal 4','Verified','POF','Classification Type','Storage Location');

$sql = "select p.*,l.LocationName as location,pkg.packageName,c.AWSCustomerID,c.CustomerName,ac.Customer,ct.Cumstomertype from pallets p
                left join location l on p.WarehouseLocationId = l.LocationID
                left join customer c on p.idCustomer = c.CustomerID
                left join aws_customers ac on ac.AWSCustomerID = p.AWSCustomerID
                left join customertype ct on ct.idCustomertype  = p.idCustomertype
                left join package pkg on p.idPackage=pkg.idPackage where LoadId = '".$data['LoadId']."' ";
$query = mysqli_query($connectionlink1, $sql);
if (mysqli_error($connectionlink1)) {
    echo mysqli_error($connectionlink1);
}
while ($row = mysqli_fetch_assoc($query)) {
    if($row['Verified'] == 1)
    {
        $row['Verified'] = 'Yes';
    }
    else if($row['Verified'] == 0)
    {
        $row['Verified'] = 'No';
    }
    if($row['POF'] == 1)
    {
        $row['POF'] = 'Yes';
    }
    else if($row['POF'] == 0)
    {
        $row['POF'] = 'No';
    }
    if($row['status'] == 7)
    {
        $row['status'] = 'Quarantine';
    }
    if ($row['status'] == '1' || $row['status'] == '2' || $row['status'] == '3' || $row['status'] == '4' || $row['status'] == '5' || $row['status'] == '6')
    {
        $row['status'] = '';
    }

    $location = $row['location']." ".$row['status'];
    $row2  = array($row['idPallet'],$row['CustomerName'],$row['packageName'],$row['pallet_netweight'],$row['Customer'],$row['MaterialType'],$row['Cumstomertype'],$row['SealNo1'],$row['SealNo2'],$row['SealNo3'],$row['SealNo4'],$row['Verified'],$row['POF'],$row['WasteClassificationType'],$location);
    $rows[] = $row2;
}

$sheet_name = 'Container List';
$style1 = array(['font-style' => 'bold'], ['font-style' => '']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->writeSheetRow($sheet_name, $rejectcreated, $style1);
$writer->writeSheetRow($sheet_name, $passcreated, $style1);
$writer->writeSheetRow($sheet_name, array(''), $col_options = ['font-style'=>'bold']);
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style' => 'bold', 'border' => 'left,right,top,bottom', 'halign' => 'center', 'valign' => 'center', 'fill' => '#eee']);
foreach ($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11, $col_options = ['border' => 'left,right,top,bottom', 'halign' => 'left']);
$writer->writeToStdOut();
exit(0);
