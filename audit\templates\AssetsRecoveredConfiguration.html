<div class="page" data-ng-controller="AssetsRecoveredConfiguration">
    <div class="row ui-section">
        <div class="col-md-12">
            <article class="article">                
                <form name="audit_form" class="form-validation">                    
                    <md-card class="no-margin-h pt-0">
                        <md-toolbar class="md-table-toolbar md-default" ng-init="Stationblock = true">
                            <div class="md-toolbar-tools" style="cursor: pointer;" ng-click="Stationblock = !Stationblock">
                                <i class="material-icons md-primary" ng-show="Stationblock">keyboard_arrow_up</i>
                                <i class="material-icons md-primary" ng-show="! Stationblock">keyboard_arrow_down</i>
                                <span>Assets Recovered Configuration</span>
                            </div>
                        </md-toolbar>

                        <div class="row" ng-show="Stationblock">

                            <div class="col-md-12">                                

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Facility</label>
                                        <md-select name="FacilityID" ng-model="config.FacilityID" required ng-disabled="true">
                                            <md-option value="{{fac.FacilityID}}" ng-repeat="fac in AllFacilities" >{{fac.FacilityName}}</md-option>
                                        </md-select>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Disposition From</label>
                                        <md-select name="FromDispositionID" ng-model="config.FromDispositionID" required >
                                            <md-option value="{{disp.disposition_id}}" ng-repeat="disp in dispositions" >{{disp.disposition}}</md-option>
                                        </md-select>
                                    </md-input-container>
                                </div>                                                                    

                               <!--  <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Recovery Type</label>
                                        <md-select name="Recoverytypeid" ng-model="config.Recoverytypeid" required >
                                            <md-option value="{{rec.Recoverytypeid}}" ng-repeat="rec in Recoverys">{{rec.Recoverytype}}</md-option>
                                        </md-select>
                                    </md-input-container>
                                </div> -->

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Workflow</label>
                                        <md-select name="workflow_id" ng-model="config.workflow_id" required ng-change="GetWorkflowInputs()">
                                            <md-option value="{{rec.workflow_id}}" ng-repeat="rec in workflows">{{rec.workflow}}</md-option>
                                        </md-select>
                                    </md-input-container>
                                </div>
                                
                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Part Type</label>
                                        <md-select name="parttypeid" ng-model="config.parttypeid" required >
                                            <md-option value="{{pt.parttypeid}}" ng-repeat="pt in PartTypes">{{pt.parttype}}</md-option>
                                        </md-select>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Evaluation Result</label>
                                        <md-select name="input_id" ng-model="config.input_id" required >
                                            <md-option value="{{ip.input_id}}" ng-repeat="ip in Inputs">{{ip.input}}</md-option>
                                        </md-select>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Disposition To</label>
                                        <md-select name="ToDispositionID" ng-model="config.ToDispositionID" required >
                                            <md-option value="{{disp.disposition_id}}" ng-repeat="disp in dispositions" >{{disp.disposition}}</md-option>
                                        </md-select>
                                    </md-input-container>
                                </div>

                                <div class="col-md-3">
                                    <md-input-container class="md-block">
                                        <label>Status</label>
                                        <md-select name="Status" ng-model="config.Status" required >
                                            <md-option value="Active" >Active</md-option>
                                            <md-option value="Inactive" >Inactive</md-option>
                                        </md-select>
                                    </md-input-container>
                                </div>

                            </div>


                            <div class="col-md-9 btns-row">
                                <button class="md-button md-raised btn-w-md  md-default" ng-click="Cancel()">Cancel</button>
                                <md-button class="md-raised btn-w-md md-primary btn-w-md"
                                    data-ng-disabled="audit_form.$invalid || config.busy" ng-click="SaveRecoveryConfiguration();">
                                    <span>Save</span>                                    
                                </md-button>                                
                            </div>


                        </div>
                    </md-card>                    
                </form>
            </article>
        </div>
    </div>
</div>