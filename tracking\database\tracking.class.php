<?php
session_start();
include_once("../../connection.php");
include_once("../../common_functions.php");
class TrackingClass extends CommonClass {
	public $responseParameters;	
	public $connectionlink;
	public function __construct(){
		$this->connectionlink = Connection::DBConnect();
	}
	
	public function GetAssetDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$query = "select A.AssetScanID,A.<PERSON>ial<PERSON>umber,A.idPallet,A.<PERSON>,D.disposition,A.<PERSON>anitizationCustomPalletID from asset A
						LEFT JOIN disposition D ON D.disposition_id = A.disposition_id
						where A.SerialNumber = '".$data['SerialNumber']."' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					if($row['StatusID'] == '10') {
						$json['Success'] = false;
						$json['Result'] = 'Serial is not committed';
						return json_encode($json);
					}
					if($row['FirstSanitizationCustomPalletID'] > 0) {//Sanitzaed Asset
						$query1 = "select sanitization_seal_id from asset_sanitization where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."' order by CreatedDate desc limit 1";
						$q1 = mysqli_query($this->connectionlink,$query1);
						if(mysqli_error($this->connectionlink)) {	
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row1 = mysqli_fetch_assoc($q1);
							$row['sanitization_seal_id'] = $row1['sanitization_seal_id'];
						}
					}
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Serial numbers available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetAssetcompleteDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$query = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition from asset A
						LEFT JOIN disposition D ON D.disposition_id = A.disposition_id
						where A.SerialNumber = '".$data['SerialNumber']."'";
			
			$query = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,A.FirstSanitizationCustomPalletID,br.rule_name,br.rule_description,br.rule_id_text from asset A
			LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
			left join business_rule br on A.rule_id = br.rule_id 
			where A.AssetScanID = '".$data['SerialNumber']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				$row = mysqli_fetch_assoc($q);

				if($row['FirstSanitizationCustomPalletID'] > 0) {//Sanitzaed Asset
					$query1 = "select sanitization_seal_id from asset_sanitization where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."' order by CreatedDate desc limit 1";
					$q1 = mysqli_query($this->connectionlink,$query1);
					if(mysqli_error($this->connectionlink)) {	
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row1 = mysqli_fetch_assoc($q1);
						$row['sanitization_seal_id'] = $row1['sanitization_seal_id'];
					}
				}
				$json['Success'] = true;
				$json['Result'] = $row;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Serial numbers available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetAssetcompleteTracking ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			//$query = "select AssetScanID from asset where SerialNumber = '".$data['SerialNumber']."'";
			$query = "select AssetScanID from asset where AssetScanID = '".$data['SerialNumber']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				$row = mysqli_fetch_assoc($q);
				$querytrack = "select AT.Action,AT.CreatedDate,U.FirstName,U.LastName from asset_tracking AT 
								LEFT JOIN users U ON U.UserId = AT.CreatedBy
								where AT.AssetScanID = '".$row['AssetScanID']."'";
				$qtrack = mysqli_query($this->connectionlink,$querytrack);
				if(mysqli_error($this->connectionlink)) {	
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					while($rowtrack = mysqli_fetch_assoc($qtrack)) {
						$dt = strtotime($rowtrack['CreatedDate']); //make timestamp with datetime string
						$rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);
						$result[$i] = $rowtrack;
						$i++;
					}
				}
				else {
					$json['Success'] = false;
					$json['Result'] = "No Tracking available";
					return json_encode($json);
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Tracking available";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetTicketDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			/*$query = "select L.LoadId,SC.CustomerName,F.FacilityName,L.DateReceived from loads L 
				LEFT JOIN customer SC ON SC.CustomerID = L.idCustomer
				LEFT JOIN facility F ON F.FacilityID = L.FacilityID
				where L.LoadId = '".$data['TicketID']."'";*/

			$query = "select L.LoadId,F.FacilityName,L.DateReceived from loads L 			
			LEFT JOIN facility F ON F.FacilityID = L.FacilityID
			where L.LoadId = '".$data['TicketID']."'";
			
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q))
				{
					if($row['DateReceived'] != '')
					{	$dt = strtotime($row['DateReceived']); //make timestamp with datetime string
						$row['DateReceived'] = date("Y-m-d H:i:s", $dt);
					}
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Tickets available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetLoadPalletDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$query = "Select P.idPallet,L.LocationName,P.SealNo1,P.ReceivedDate,SC.CustomerName,P.LoadId from pallets P 
				LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer 
				LEFT JOIN location L ON L.LocationID = P.WarehouseLocationId 
				WHERE P.LoadId = '".$data['LoadID']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q))
				{
					$dt = strtotime($row['ReceivedDate']); //make timestamp with datetime string
					$row['ReceivedDate'] = date("Y-m-d H:i:s", $dt);
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Containers available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetPalletAssetDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$query = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition from asset A
						LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
						where A.idPallet = '".$data['PalletID']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q))
				{
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Assets available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetLoadcompleteDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$query = "select L.LoadId,F.FacilityName,L.DateReceived from loads L 
						LEFT JOIN facility F ON F.FacilityID = L.FacilityID 
						where L.LoadId = '".$data['LoadId']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				$dt = strtotime($row['DateReceived']); //make timestamp with datetime string
				$row['DateReceived'] = date("Y-m-d H:i:s", $dt);
				$row = mysqli_fetch_assoc($q);
				$json['Success'] = true;
				$json['Result'] = $row;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Tickets available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetLoadcompleteTracking ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$querytrack = "select LT.Action,LT.CreatedDate,U.FirstName,U.LastName from load_tracking LT 
							LEFT JOIN users U ON U.UserId = LT.CreatedBy
							where LT.LoadId = '".$data['LoadId']."'";
			$qtrack = mysqli_query($this->connectionlink,$querytrack);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($rowtrack = mysqli_fetch_assoc($qtrack)) {
					$dt = strtotime($rowtrack['CreatedDate']); //make timestamp with datetime string
					$rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);
					$result[$i] = $rowtrack;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			}
			else {
				$json['Success'] = false;
				$json['Result'] = "No Tracking available";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetPalletcompleteDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$query = "Select P.idPallet,L.LocationName,P.SealNo1,P.ReceivedDate,SC.CustomerName from pallets P 
						LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer 
						LEFT JOIN location L ON L.LocationID = P.WarehouseLocationId
						WHERE P.idPallet = '".$data['PalletID']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				$dt = strtotime($row['ReceivedDate']); //make timestamp with datetime string
				$row['ReceivedDate'] = date("Y-m-d H:i:s", $dt);
				$row = mysqli_fetch_assoc($q);
				$json['Success'] = true;
				$json['Result'] = $row;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Container available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetPalletcompleteTracking ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$querytrack = "select PT.Action,PT.CreatedDate,U.FirstName,U.LastName from pallet_tracking PT 
							LEFT JOIN users U ON U.UserId = PT.CreatedBy
							where PT.idPallet = '".$data['PalletID']."'";
			$qtrack = mysqli_query($this->connectionlink,$querytrack);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($rowtrack = mysqli_fetch_assoc($qtrack)) {
					$dt = strtotime($rowtrack['CreatedDate']); //make timestamp with datetime string
					$rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);
					$result[$i] = $rowtrack;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
				return json_encode($json);
			}
			else {
				$json['Success'] = false;
				$json['Result'] = "No Tracking available";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}
	public function GetPalletDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$query = "Select P.idPallet,L.LocationName,P.SealNo1,P.ReceivedDate,P.LoadId,SC.CustomerName from pallets P 
						LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer  
						LEFT JOIN location L ON L.LocationID = P.WarehouseLocationId 
						WHERE P.idPallet = '".$data['PalletID']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q))
				{
					$dt = strtotime($row['ReceivedDate']); //make timestamp with datetime string
					$row['ReceivedDate'] = date("Y-m-d H:i:s", $dt);
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Containers available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function RecordUserTrackingActivity($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$transaction = $data['Type'];
		$description = '';
		if($data['SerialNumber']) {
			$description = 'Serial Number ('.$data['SerialNumber'].') Tracking Exported';
		} else if($data['LoadId']) {
			$description = 'Inbound Ticket ID ('.$data['LoadId'].') Tracking Exported';
		} else if($data['PalletID']) {
			$description = 'Container ID ('.$data['PalletID'].') Tracking Exported';
		}
		$this->RecordUserTransaction($transaction,$description);

		$json['Success'] = true;
		$json['Result'] = "Transaction Recorded";
		return json_encode($json);
	}


	public function GetSNType ($data) {		
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			//Start check If Serial is Media or not
			$query = "select count(*) from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['TrackSN'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['count(*)'] > 0) {

					//Start check If media is from SPEED or not
					// $query7 = "select p.MaterialType from asset m 
					// left join pallets p on m.idPallet = p.idPallet 
					// where m.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['TrackSN'])."'";
					// $q7 = mysqli_query($this->connectionlink,$query7);
					// if(mysqli_error($this->connectionlink)) {	
					// 	$json['Success'] = false;
					// 	$json['Result'] = mysqli_error($this->connectionlink);
					// 	return json_encode($json);
					// }
					// if(mysqli_affected_rows($this->connectionlink) > 0) {
					// 	$row7 = mysqli_fetch_assoc($q7);
					// 	if($row7['MaterialType'] == 'Media Rack') {
					// 		$json['Success'] = false;
					// 		$json['Result'] = 'Scanned serial is SPEED serial, use SPEED Tracking screen ';
					// 		return json_encode($json);
					// 	}
					// }
					//End check If media is from SPEED or not

					//Start get Asset Details					
					$query11 = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype,p.MaterialType,A.FirstSanitizationCustomPalletID,br.rule_name,br.rule_description,br.rule_id_text from asset A
					LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
					LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
					LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
					left join pallets p on A.idPallet = p.idPallet 
					left join business_rule br on A.rule_id = br.rule_id 
					where A.SerialNumber = '".$data['TrackSN']."'";
					$q11 = mysqli_query($this->connectionlink,$query11);
					if(mysqli_error($this->connectionlink)) {	
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					$asset = array();
					$k = 0;
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						while($row11 = mysqli_fetch_assoc($q11)) {
							$asset[$k] = $row11;
							if($row11['MaterialType'] == 'Media Rack') {
								$json['Success'] = false;
								$json['Result'] = 'Scanned serial is SPEED serial, use SPEED Tracking screen ';
								return json_encode($json);
							}
							$row11['ItemType'] = 'Asset';


							if($row11['FirstSanitizationCustomPalletID'] > 0) {//Sanitzaed Asset
								$query1 = "select sanitization_seal_id from asset_sanitization where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row11['AssetScanID'])."' order by CreatedDate desc limit 1";
								$q1 = mysqli_query($this->connectionlink,$query1);
								if(mysqli_error($this->connectionlink)) {	
									$json['Success'] = false;
									$json['Result'] = mysqli_error($this->connectionlink);
									return json_encode($json);
								}
								if(mysqli_affected_rows($this->connectionlink) > 0) {
									$row1 = mysqli_fetch_assoc($q1);
									$asset[$k]['sanitization_seal_id'] = $row1['sanitization_seal_id'];
								}
							}
							$k++;
						}
						//$row11 = mysqli_fetch_assoc($q11);						


						$json['Asset'] = $asset;
					}					
					//End get Asset Details

					$json['Success'] = true;
					$json['SerialType'] = 'Asset';
					$json['SN'] = $data['TrackSN'];
					return json_encode($json);
				} else { // Start check If Serial is Container

					$query1 = "select count(*) from pallets where idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['TrackSN'])."'";
					$q1 = mysqli_query($this->connectionlink,$query1);
					if(mysqli_error($this->connectionlink)) {	
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}

					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row1 = mysqli_fetch_assoc($q1);
						if($row1['count(*)'] > 0) {


							//Start check If media is from SPEED or not
							$query7 = "select p.MaterialType from pallets p where p.idPallet = '".mysqli_real_escape_string($this->connectionlink,$data['TrackSN'])."'";
							$q7 = mysqli_query($this->connectionlink,$query7);
							if(mysqli_error($this->connectionlink)) {	
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row7 = mysqli_fetch_assoc($q7);
								if($row7['MaterialType'] == 'Media Rack') {
									$json['Success'] = false;
									$json['Result'] = 'Scanned serial is SPEED Rack, use SPEED Tracking screen ';
									return json_encode($json);
								}
							}
							//End check If media is from SPEED or not

							$json['Success'] = true;
							$json['SerialType'] = 'Container';
							$json['SN'] = $data['TrackSN'];
							return json_encode($json);
						} else {// Start check If Serial is Ticket ID
							$query2 = "select count(*) from loads where LoadId = '".mysqli_real_escape_string($this->connectionlink,$data['TrackSN'])."' ";
							$q2 = mysqli_query($this->connectionlink,$query2);
							if(mysqli_error($this->connectionlink)) {	
								$json['Success'] = false;
								$json['Result'] = mysqli_error($this->connectionlink);
								return json_encode($json);
							}
							if(mysqli_affected_rows($this->connectionlink) > 0) {
								$row2 = mysqli_fetch_assoc($q2);
								if($row2['count(*)'] > 0) {
									//Start get Load Details

									$query14 = "select L.LoadId,F.FacilityName,L.DateReceived from loads L 			
									LEFT JOIN facility F ON F.FacilityID = L.FacilityID
									where L.LoadId = '".$data['TrackSN']."'";
									
									$q14 = mysqli_query($this->connectionlink,$query14);
									if(mysqli_error($this->connectionlink)) {	
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}
									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$i = 0;
										while($row14 = mysqli_fetch_assoc($q14))
										{
											if($row14['DateReceived'] != '')
											{	$dt = strtotime($row14['DateReceived']); //make timestamp with datetime string
												$row14['DateReceived'] = date("Y-m-d H:i:s", $dt);
											}
											$result[$i] = $row14;
											$i++;
										}										
										$json['Asset'] = $result;
									} else {
										$json['Success'] = false;
										$json['Result'] = "No Tickets available";
										return json_encode($json);
									}

									//End get Load Details

									$json['Success'] = true;
									$json['SerialType'] = 'Load';
									$json['SN'] = $data['TrackSN'];
									return json_encode($json);
								} else { //Start check If Serial is Server Recovery
									$asset = array();
									$query12 = "select count(*) from speed_server_recovery where ServerSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['TrackSN'])."'";
									$q12 = mysqli_query($this->connectionlink,$query12);
									if(mysqli_error($this->connectionlink)) {	
										$json['Success'] = false;
										$json['Result'] = mysqli_error($this->connectionlink);
										return json_encode($json);
									}

									if(mysqli_affected_rows($this->connectionlink) > 0) {
										$row12 = mysqli_fetch_assoc($q12);
										if($row12['count(*)'] > 0) {

											$query13 = "select A.ServerID,A.ServerSerialNumber as SerialNumber,A.idPallet,A.MPN as UniversalModelNumber,D.disposition,pt.parttype,p.MaterialType,br.rule_name,br.rule_description,br.rule_id_text from speed_server_recovery A
											LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
											LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid  
											left join pallets p on A.idPallet = p.idPallet 
											left join business_rule br on A.rule_id = br.rule_id 
											where A.ServerSerialNumber = '".$data['TrackSN']."'";											
											$q13 = mysqli_query($this->connectionlink,$query13);
											if(mysqli_error($this->connectionlink)) {	
												$json['Success'] = false;
												$json['Result'] = mysqli_error($this->connectionlink);
												return json_encode($json);												
											}
											$row13 = mysqli_fetch_assoc($q13);
											$row13['Recoverytype'] = 'Rack';
											$asset[0] = $row13;
											if($row11['MaterialType'] == 'Media Rack') {
												$json['Success'] = false;
												$json['Result'] = 'Scanned serial is SPEED serial, use SPEED Tracking screen ';
												return json_encode($json);
											}
											$row11['ItemType'] = 'Server';
											$json['Asset'] = $asset;
											

											$json['Success'] = true;
											$json['SerialType'] = 'Server';
											$json['SN'] = $data['SpeedSN'];
											return json_encode($json);
										}  else {//Start check If Serial is Media Recovery
											$asset = array();
											$query13 = "select count(*) from speed_media_recovery where MediaSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['TrackSN'])."'";
											$q13 = mysqli_query($this->connectionlink,$query13);
											if(mysqli_error($this->connectionlink)) {	
												$json['Success'] = false;
												$json['Result'] = mysqli_error($this->connectionlink);
												return json_encode($json);
											}

											if(mysqli_affected_rows($this->connectionlink) > 0) {
												$row13 = mysqli_fetch_assoc($q13);
												if($row13['count(*)'] > 0) {
													$query14 = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype,p.MaterialType,br.rule_name,br.rule_description,br.rule_id_text from speed_media_recovery A
													LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
													LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
													left join pallets p on A.idPallet = p.idPallet 
													left join business_rule br on A.rule_id = br.rule_id 
													where A.MediaSerialNumber = '".$data['TrackSN']."'";
													$q14 = mysqli_query($this->connectionlink,$query14);
													if(mysqli_error($this->connectionlink)) {	
														$json['Success'] = false;
														$json['Result'] = mysqli_error($this->connectionlink);
														return json_encode($json);												
													}
													$row14 = mysqli_fetch_assoc($q14);
													$row14['Recoverytype'] = 'Assembly';
													$asset[0] = $row14;
													if($row11['MaterialType'] == 'Media Rack') {
														$json['Success'] = false;
														$json['Result'] = 'Scanned serial is SPEED serial, use SPEED Tracking screen ';
														return json_encode($json);
													}
													$row11['ItemType'] = 'Media';
													$json['Asset'] = $asset;
													

													$json['Success'] = true;
													$json['SerialType'] = 'Media';
													$json['SN'] = $data['TrackSN'];
													return json_encode($json);


												} else {
													$json['Success'] = false;
													$json['Result'] = 'Scanned Serial is not a valid Serial';
													return json_encode($json);
												}
											} else {
												$json['Success'] = false;
												$json['Result'] = 'Scanned Serial is not a valid Serial';
												return json_encode($json);
											}
										}
									} else {
										$json['Success'] = false;
										$json['Result'] = 'Scanned Serial is not a valid Serial';
										return json_encode($json);	
									}									
								}
							} else {	
								$json['Success'] = false;
								$json['Result'] = 'Scanned Serial is not a valid Serial';
								return json_encode($json);							
							}
						} 
					} else {
						$json['Success'] = false;
						$json['Result'] = 'Scanned Serial is not a valid Serial';
						return json_encode($json);
					}

				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Scanned Serial is not a valid Serial';
				return json_encode($json);
			}	
		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function GetContainerRecoveryTracking ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$query = "Select P.idPallet,L.LocationName,P.SealNo1,P.ReceivedDate,P.LoadId,SC.CustomerName,PS.StatusValue from pallets P 
						LEFT JOIN customer SC ON SC.CustomerID = P.idCustomer  
						LEFT JOIN location L ON L.LocationID = P.WarehouseLocationId 
						LEFT JOIN pallet_status PS on PS.status = P.status 
						WHERE P.idPallet = '".$data['PalletID']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				while($row = mysqli_fetch_assoc($q))
				{
					$dt = strtotime($row['ReceivedDate']); //make timestamp with datetime string
					$row['ReceivedDate'] = date("Y-m-d H:i:s", $dt);
					$result[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $result;
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Containers available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function GetContainerRecovery ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$result = array();
			$i = 0;
			$query = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype from asset A
						LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
						LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
						where A.idPallet = '".$data['PalletID']."' and (r.Recoverytype = 'Container' or isnull(A.Recoverytypeid))";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {				
				while($row = mysqli_fetch_assoc($q)) {
					$result[$i] = $row;
					$i++;
				}				
			} 

			$query = "select A.ServerID,A.ServerSerialNumber as SerialNumber,A.idPallet,A.MPN as UniversalModelNumber,D.disposition,pt.parttype from speed_server_recovery A
			LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
			LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
			where A.idPallet = '".$data['PalletID']."'";

			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				
				while($row = mysqli_fetch_assoc($q)) {					
					$row['Recoverytype'] = 'Rack';
					$row['ItemType'] = 'Server';
					$result[$i] = $row;
					$i++;
				}				
			}
			
			if(count($result) == 0){
				$json['Success'] = false;
				$json['Result'] = "No Serials available";
				return json_encode($json);
			}

			$json['Success'] = true;
			$json['Result'] = $result;			
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function GetNextLevelRecovery ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			// if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'No Access to Tracking Page';
			// 	return json_encode($json);
			// }
			$result = array();
			$i = 0;

			//Start get Asset Records
			if($data['AssetScanID'] > 0) {
				$query = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
						LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
						LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
						LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
						where A.TopLevelAssetScanID = '".$data['AssetScanID']."'";	
			} else {
				$query = "select A.AssetScanID,A.SerialNumber,A.idPallet,A.UniversalModelNumber,D.disposition,r.Recoverytype,pt.parttype from asset A
						LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 
						LEFT JOIN Recoverytype r on A.Recoverytypeid = r.Recoverytypeid 
						LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
						where A.TopLevelSerial = '".$data['SerialNumber']."'";
			}
			
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				
				while($row = mysqli_fetch_assoc($q)) {
					$row['ItemType'] = 'Asset';
					$result[$i] = $row;
					$i++;
				}				
			}
			//End get Asset Records



			//Start get Server Recovery Records
			$query = "select A.ServerID,A.ServerSerialNumber as SerialNumber,A.idPallet,A.MPN as UniversalModelNumber,D.disposition,pt.parttype from speed_server_recovery A
						LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
						LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
						where A.idPallet = '".$data['SerialNumber']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				
				while($row = mysqli_fetch_assoc($q)) {					
					$row['Recoverytype'] = 'Rack';
					$row['ItemType'] = 'Server';
					$result[$i] = $row;
					$i++;
				}				
			}
			//End get Server Recovery Records


			//Start get Media Recovery Records
			$query = "select A.MediaID,A.MediaSerialNumber as SerialNumber,A.idPallet,A.MediaMPN as UniversalModelNumber,D.disposition,pt.parttype from speed_media_recovery A
						LEFT JOIN disposition D ON D.disposition_id = A.disposition_id 						
						LEFT JOIN parttype pt on A.parttypeid = pt.parttypeid 
						where A.ServerSerialNumber = '".$data['SerialNumber']."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				
				while($row = mysqli_fetch_assoc($q)) {
					$row['Recoverytype'] = 'Assembly';
					$row['ItemType'] = 'Media';
					$result[$i] = $row;
					$i++;
				}				
			}
			//End get Media Recovery Records

			// if(count($result) == 0) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'No serials available';
			// 	return json_encode($json);
			// }


			$json['Success'] = true;
			$json['Result'] = $result;
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function GetMediaLifeCycle ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$querytrack = "select AT.Action,AT.CreatedDate,U.FirstName,U.LastName from speed_media_recovery_tracking AT 
            LEFT JOIN users U ON U.UserId = AT.CreatedBy 
            where AT.MediaID = '".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."' order by CreatedDate";
            $qtrack = mysqli_query($this->connectionlink,$querytrack);
            if(mysqli_error($this->connectionlink)) {	
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				$result = array();
                while($rowtrack = mysqli_fetch_assoc($qtrack)) {
                    $dt = strtotime($rowtrack['CreatedDate']); //make timestamp with datetime string
                    $rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);
                    $result[$i] = $rowtrack;
                    $i++;
                }
            } else {
                $json['Success'] = false;
                $json['Result'] = "No Tracking available";
                return json_encode($json);
            }
            $json['Success'] = true;
            $json['Result'] = $result;
            return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}



	public function GetServerLifeCycle ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Tracking')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Tracking Page';
				return json_encode($json);
			}
			$querytrack = "select AT.Action,AT.CreatedDate,U.FirstName,U.LastName from speed_server_recovery_tracking AT 
            LEFT JOIN users U ON U.UserId = AT.CreatedBy 
            where AT.ServerID = '".mysqli_real_escape_string($this->connectionlink,$data['ServerID'])."' order by CreatedDate";
            $qtrack = mysqli_query($this->connectionlink,$querytrack);
            if(mysqli_error($this->connectionlink)) {	
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
                while($rowtrack = mysqli_fetch_assoc($qtrack)) {
                    $dt = strtotime($rowtrack['CreatedDate']); //make timestamp with datetime string
                    $rowtrack['CreatedDate'] = date("Y-m-d H:i:s", $dt);
                    $result[$i] = $rowtrack;
                    $i++;
                }
            } else {
                $json['Success'] = false;
                $json['Result'] = "No Tracking available";
                return json_encode($json);
            }
            $json['Success'] = true;
            $json['Result'] = $result;
            return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GenerateExportTrackRecordsXLS($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => ''
		);
        $query = "select * from pallets where idPallet = '".$data['TrackSN']."'";
        $q = mysqli_query($this->connectionlink,$query);
		if(mysqli_error($this->connectionlink)) {
			$json['Success'] = false;
			$json['Result'] = mysqli_error($this->connectionlink);
			return json_encode($json);
		}
		if(mysqli_affected_rows($this->connectionlink) > 0) {
			$_SESSION['idPallet'] = $data['TrackSN'];
			$json['Success'] = true;
			$json['Result'] = 1;
			return json_encode($json);
		} else {
			$json['Success'] = false;
			$json['Result'] = 'No Data';
			return json_encode($json);
		}
		return json_encode($json);
	}

}
?>