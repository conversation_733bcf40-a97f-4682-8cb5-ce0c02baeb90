<?php
session_start();
include_once("audit.class.php");
class PendingMediaClass extends AuditClass {
    
    public function GetPendingMedia ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Media')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Media Page';
				return json_encode($json);
			}

            $query = "select m.*,d.disposition,s.Status,c.BinName,u.FirstName,u.LastName,f.FacilityName,si.SiteName,p.LoadId,p.ReceivedDate from speed_media_recovery m 
            left join disposition d on m.disposition_id = d.disposition_id  
            left join speed_status s on m.StatusID = s.StatusID 
            left join custompallet c on m.CustomPalletID = c.CustomPalletID 
            left join users u on m.CreatedBy = u.UserId   
            left join facility f on m.FacilityID = f.FacilityID 
            left join site si on m.SiteID = si.SiteID 
            left join pallets p on m.idPallet = p.idPallet 
            where m.StatusID in (1,2,4) and m.FacilityID = '".$_SESSION['user']['FacilityID']."' ";

			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {

						if($key == 'idPallet') {
							$query = $query . " AND m.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ServerSerialNumber') {
							$query = $query . " AND m.ServerSerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'MediaSerialNumber') {
							$query = $query . " AND m.MediaSerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'MediaType') {
							$query = $query . " AND m.MediaType like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'disposition') {
							$query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'Status') {
							$query = $query . " AND s.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
                        if($key == 'BinName') {
							$query = $query . " AND c.BinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
                        if($key == 'FirstName') {
							$query = $query . " AND u.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
                        if($key == 'AuditControllerID') {
							$query = $query . " AND m.AuditControllerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}


                        if($key == 'FacilityName') {
							$query = $query . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
                        if($key == 'SiteName') {
							$query = $query . " AND si.SiteName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
                        if($key == 'LoadId') {
							$query = $query . " AND p.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
                        if($key == 'ReceivedDate') {
							$query = $query . " AND p.ReceivedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'CreatedDate') {
							$query = $query . " AND m.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
									
					}
				}
			}
			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}
	
				if($data['OrderBy'] == 'idPallet') {
					$query = $query . " order by m.idPallet ".$order_by_type." ";
				}
				else if($data['OrderBy'] == 'ServerSerialNumber') {
					$query = $query . " order by m.ServerSerialNumber ".$order_by_type." ";
				}  
				else if($data['OrderBy'] == 'MediaSerialNumber') {
					$query = $query . " order by m.MediaSerialNumber ".$order_by_type." ";
				} 
				else if($data['OrderBy'] == 'MediaType') {
					$query = $query . " order by m.MediaType ".$order_by_type." ";
				}
				else if($data['OrderBy'] == 'disposition') {
					$query = $query . " order by d.disposition ".$order_by_type." ";
				}
				else if($data['OrderBy'] == 'Status') {
					$query = $query . " order by s.Status ".$order_by_type." ";
				}
                else if($data['OrderBy'] == 'BinName') {
					$query = $query . " order by c.BinName ".$order_by_type." ";
				}
                else if($data['OrderBy'] == 'FirstName') {
					$query = $query . " order by u.FirstName ".$order_by_type." ";
				}
                else if($data['OrderBy'] == 'AuditControllerID') {
					$query = $query . " order by m.AuditControllerID ".$order_by_type." ";
				}

                else if($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by f.FacilityName ".$order_by_type." ";
				}
                else if($data['OrderBy'] == 'SiteName') {
					$query = $query . " order by si.SiteName ".$order_by_type." ";
				}
                else if($data['OrderBy'] == 'LoadId') {
					$query = $query . " order by p.LoadId ".$order_by_type." ";
				}
                else if($data['OrderBy'] == 'ReceivedDate') {
					$query = $query . " order by p.ReceivedDate ".$order_by_type." ";
				}
				else if($data['OrderBy'] == 'CreatedDate') {
					$query = $query . " order by m.CreatedDate ".$order_by_type." ";
				}
				
			} else {
				$query = $query . " order by CreatedDate asc ";
			}			

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));

			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				//$assets = array();
				while($row = mysqli_fetch_assoc($q)) {
					$assets[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $assets;
				//return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No Assets Available';
				//return json_encode($json);
			}
		      if($data['skip'] == 0) {
                $query1 = "select count(*) from speed_media_recovery m 
                left join disposition d on m.disposition_id = d.disposition_id  
                left join speed_status s on m.StatusID = s.StatusID 
                left join custompallet c on m.CustomPalletID = c.CustomPalletID 
                left join users u on m.CreatedBy = u.UserId   
                left join facility f on m.FacilityID = f.FacilityID 
                left join site si on m.SiteID = si.SiteID 
                left join pallets p on m.idPallet = p.idPallet 
                where m.StatusID in (1,2,4) and m.FacilityID = '".$_SESSION['user']['FacilityID']."' ";

				if($data[0] && count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if($value != '') {
							
							if($key == 'idPallet') {
                                $query1 = $query1 . " AND m.idPallet like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'ServerSerialNumber') {
                                $query1 = $query1 . " AND m.ServerSerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'MediaSerialNumber') {
                                $query1 = $query1 . " AND m.MediaSerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'MediaType') {
                                $query1 = $query1 . " AND m.MediaType like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'disposition') {
                                $query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'Status') {
                                $query1 = $query1 . " AND s.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'BinName') {
                                $query1 = $query1 . " AND c.BinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'FirstName') {
                                $query1 = $query1 . " AND u.FirstName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'AuditControllerID') {
                                $query1 = $query1 . " AND m.AuditControllerID like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }

                            if($key == 'FacilityName') {
                                $query1 = $query1 . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'SiteName') {
                                $query1 = $query1 . " AND si.SiteName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'LoadId') {
                                $query1 = $query1 . " AND p.LoadId like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'ReceivedDate') {
                                $query1 = $query1 . " AND p.ReceivedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
							if($key == 'CreatedDate') {
                                $query1 = $query1 . " AND m.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
						}
					}
				}

				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}   

	public function GeneratePendingMediaPannelxls($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Media')) {
			$json['Success'] = false;
			$json['Result'] = 'No Access to Pending Media Page';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);
		$_SESSION['PendingMediaPannelxls'] = $data;
		$json['Success'] = true;
		//$json['Result'] = $result;
		return json_encode($json);
	}

	public function ValidateNextBinID($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		try {			
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Media')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Media Page';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data
			);	

			$query = "select c.*,d.disposition from custompallet c 
			left join disposition  d on  c.disposition_id = d.disposition_id 
			where c.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinID'])."' ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				// if($row['InventoryBased'] == '1') {
				// 	$json['Success'] = false;
				// 	$json['Error'] = 'BIN is dedicated for Sub Component';
				// 	return json_encode($json);
				// }
				if($row['StatusID'] != '1') {					
					$json['Success'] = false;
					$json['Error'] = 'BIN Status is not Active';
					return json_encode($json);
				}

				if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {									
					$json['Success'] = false;
					$json['Error'] = 'BIN Facility is different from Users Facility';
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['CustomPalletID'] = $row['CustomPalletID'];
				$json['BinName'] = $row['BinName'];
				$json['disposition_id'] = $row['disposition_id'];
				if($row['AcceptAllDisposition'] == '1') {
					$json['disposition'] = 'All Disposition';	
				} else {
					$json['disposition'] = $row['disposition'];
				}				
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}	
	}


	public function ProcessMediaSN($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		try {			
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Media')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Media Page';
				return json_encode($json);
			}

			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Pending Media')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access Pending Media Page';
				return json_encode($json);
			}

			$json = array(
				'Success' => false,
				'Result' => $data
			);
			//return json_encode($json);
			//Start validate Audit Controller

			$query = "select * from users where UserName = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."' and Password = '".mysqli_real_escape_string($this->connectionlink,$data['Password'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['Status'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not active";
					return json_encode($json);
				}
				if($row['PendingMediaController'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "User is not Pending Media Controller";
					return json_encode($json);
				}

				if($row['UserId'] == $_SESSION['user']['UserId']) {
					$json['Success'] = false;
					$json['Result'] = "Controller should be different from logged in user";
					return json_encode($json);
				}				
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Pending Media Controller or Password";
				return json_encode($json);
			}

			//End validate Audit Controller

			if($data['NewCustomPalletID'] > 0) {

				//Start get to_cp_details
				$query22 = "select c.*,d.disposition from custompallet c 
				left join disposition  d on  c.disposition_id = d.disposition_id 
				where c.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['NewCustomPalletID'])."' ";
				$q22 = mysqli_query($this->connectionlink,$query22);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$to_cp = mysqli_fetch_assoc($q22);
					if($to_cp['MobilityName'] == NULL || $to_cp['MobilityName'] == '') {
						$json['Success'] = false;
						$json['Result'] = "Mobility Name is not configured for Next BIN ID";
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Next BIN ID";
					return json_encode($json);	
				}
				//End get to_cp_details

				//Start get from_cp_details
				$query11 = "select c.* from custompallet c where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
				$q11 = mysqli_query($this->connectionlink,$query11);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$from_cp = mysqli_fetch_assoc($q11);									
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid From BIN";
					return json_encode($json);
				}
				//End get from_cp_details		
				
				$from_disposition_id = $data['disposition_id'];
				$from_status = $data['StatusID'];
				$from_CustomPalletID = $data['CustomPalletID'];

				$to_disposition_id = $to_cp['disposition_id'];
				if($data['StatusID'] == '1') {
					$to_status = '2';
					$to_status_text = 'PendingShred';
				} else if($data['StatusID'] == '2') {
					$to_status = '3';
					$to_status_text = 'Shreded';
				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Media Status";
					return json_encode($json);
				}
				$to_CustomPalletID = $to_cp['CustomPalletID'];

				//Start send Custody SNS
				$message_cus = '
				{
					"eventType": "BATCH_MEDIA_DRIVES_IN_CUSTODY",
					"data": {
					"site": "'.$_SESSION['user']['FacilityName'].'",
					"login": "'.$_SESSION['user']['UserName'].'",
					"media": [
						{
						"serial": "'.$data['MediaSerialNumber'].'",
						"type": "'.$data['MediaType'].'",						
						"timestamp": "'.time().'"
						}
						]
					}
				}';
				$event_type_cus = "BATCH_MEDIA_DRIVES_IN_CUSTODY";

				if($from_cp['BinType'] == 'Physical') {
					$SNS_Message_cus = $this->SendSNSMessage($message_cus,$data['MediaSerialNumber'],$event_type_cus,'MEDIA',$data['MediaType'],NULL,$data['idPallet'],NULL,$data['MediaID']);
					if($SNS_Message_cus['Success'] != true) {
						$json['Success'] = false;
						$json['Result'] = 'Custody SNS Message Failed, Holding on Processing Media';
						return json_encode($json);	
					}	
				}				
				//End send Custody SNS

				//Start calling SNS function

				$message_degauss = '
				{
					"eventType": "BATCH_MEDIA_DRIVES_DEGAUSSED",
					"data": {
					"site": "'.$_SESSION['user']['FacilityName'].'",
					"login": "'.$_SESSION['user']['UserName'].'",
					"media": [
						{
						"serial": "'.$data['MediaSerialNumber'].'",
						"type": "'.$data['MediaType'].'",
						"binId": "'.$to_cp['BinName'].'",
						"timestamp": "'.time().'"
						}
						]
					}
				}';
				$event_type_degauss = "BATCH_MEDIA_DRIVES_DEGAUSSED";


				if($data['StatusID'] == '1') { //IF current status is Pending Degauss
					// $message = '
					// {
					// 	"eventType": "SINGLE_MEDIA_DRIVE_DEGAUSSED",
					// 	"data": {
					// 	"site": "'.$_SESSION['user']['FacilityName'].'",
					// 	"login": "'.$_SESSION['user']['UserName'].'",
					// 	"media": 
					// 		{
					// 		"serial": "'.$data['MediaSerialNumber'].'",
					// 		"type": "'.$data['MediaType'].'",
					// 		"binId": "'.$to_cp['BinName'].'",
					// 		"timestamp": "'.time().'"
					// 		}
					// 	}
					// }';
					// $event_type = "SINGLE_MEDIA_DRIVE_DEGAUSSED";


					$message = '
					{
						"eventType": "BATCH_MEDIA_DRIVES_DEGAUSSED",
						"data": {
						"site": "'.$_SESSION['user']['FacilityName'].'",
						"login": "'.$_SESSION['user']['UserName'].'",
						"media": [
							{
							"serial": "'.$data['MediaSerialNumber'].'",
							"type": "'.$data['MediaType'].'",
							"binId": "'.$to_cp['BinName'].'",
							"timestamp": "'.time().'"
							}
							]
						}
					}';
					$event_type = "BATCH_MEDIA_DRIVES_DEGAUSSED";

				} else if($data['StatusID'] == '2') { //IF current status is Pending Degauss
					// $message = '
					// {
					// 	"eventType": "SINGLE_MEDIA_DRIVE_DESTROYED",
					// 	"data": {
					// 	"site": "'.$_SESSION['user']['FacilityName'].'",
					// 	"login": "'.$_SESSION['user']['UserName'].'",
					// 	"media": 
					// 		{
					// 		"serial": "'.$data['MediaSerialNumber'].'",
					// 		"type": "'.$data['MediaType'].'",
					// 		"binId": "'.$to_cp['MobilityName'].'",
					// 		"timestamp": "'.time().'"
					// 		}
					// 	}
					// }';
					// $event_type = "SINGLE_MEDIA_DRIVE_DESTROYED";


					$message = '
					{
						"eventType": "BATCH_MEDIA_DRIVES_DESTROYED",
						"data": {
						"site": "'.$_SESSION['user']['FacilityName'].'",
						"login": "'.$_SESSION['user']['UserName'].'",
						"media": [
							{
							"serial": "'.$data['MediaSerialNumber'].'",
							"type": "'.$data['MediaType'].'",
							"binId": "'.$to_cp['MobilityName'].'",
							"timestamp": "'.time().'"
							}
							]
						}
					}';
					$event_type = "BATCH_MEDIA_DRIVES_DESTROYED";

				} else {
					$json['Success'] = false;
					$json['Result'] = "Invalid Media Status";
					return json_encode($json);
				}
				if($from_cp['BinType'] == 'Physical') {
					sleep(2);
				}
				if($data['MediaType'] == 'HDD') {//For HDD send Degauss SNS first, this is not required ticket 1985
					// $SNS_Message = $this->SendSNSMessage($message_degauss,$data['MediaSerialNumber'],$event_type_degauss,'MEDIA',$data['MediaType'],$to_cp['BinName'],$data['idPallet'],NULL,$data['MediaID']);
					// if($SNS_Message['Success'] != true) {
					// 	$json['Success'] = false;
					// 	$json['Result'] = 'Degauss SNS Message Failed, Holding on Processing Media';
					// 	return json_encode($json);	
					// }
					// sleep(2);
				}
				$SNS_Message = $this->SendSNSMessage($message,$data['MediaSerialNumber'],$event_type,'MEDIA',$data['MediaType'],$to_cp['BinName'],$data['idPallet'],NULL,$data['MediaID']);
				if($SNS_Message['Success'] != true) {
					$json['Success'] = false;
					$json['Result'] = 'SNS Message Failed, Holding on Processing Media';
					return json_encode($json);	
				}

				//End calling SNS function
				
				//Start update Media SN
				$query1 = "update speed_media_recovery set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',AuditControllerID = '".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."',StatusID = '".mysqli_real_escape_string($this->connectionlink,$to_status)."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				//End update Media SN

				//Start update Custom Pallet Items
				$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."' where MediaID = '".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."'";
				$q2 = mysqli_query($this->connectionlink,$query2);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Items

				//Start update Custom Pallet Counts
				$query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."'";
				$q3 = mysqli_query($this->connectionlink,$query3);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				$query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."'";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				//End update Custom Pallet Counts

				//Start insert into media process
				$data['OriSerialNumber'] = $data['ServerSerialNumber'];
				$data['ServerSerialNumber'] = preg_replace('/[^A-Za-z0-9]/', '', $data['ServerSerialNumber']);
				$query5 = "insert into speed_media_process (MediaID,idPallet,ServerSerialNumber,MediaSerialNumber,MediaType,from_disposition_id,to_disposition_id,from_status,to_status,from_CustomPalletID,to_CustomPalletID,AuditControllerID,CreatedDate,CreatedBy,ProcessType,ActualSerialNumber,serial_scan_time,destruction_type,controller_scan_time,batch_event,to_bin_scan_time) values ('".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$data['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$from_disposition_id)."','".mysqli_real_escape_string($this->connectionlink,$to_disposition_id)."','".mysqli_real_escape_string($this->connectionlink,$from_status)."','".mysqli_real_escape_string($this->connectionlink,$to_status)."','".mysqli_real_escape_string($this->connectionlink,$from_CustomPalletID)."','".mysqli_real_escape_string($this->connectionlink,$to_CustomPalletID)."','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',NOW(),'".$_SESSION['user']['UserId']."','Manual','".$data['OriSerialNumber']."','".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."','Pending Media','".mysqli_real_escape_string($this->connectionlink,$data['controller_scan_time'])."','No','".mysqli_real_escape_string($this->connectionlink,$data['to_bin_scan_time'])."')";
				$q5 = mysqli_query($this->connectionlink,$query5);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink).$query5;
					return json_encode($json);
				}
				//End insert into media process

				//Start enter media tracking	
				$action = $data['MediaType']." Processed manually in Pending Media screen, moved from BIN (".$from_cp['BinName'].") to BIN (".$to_cp['BinName']."), Status changed to (".$to_status_text."), disposition changed to (".$to_cp['disposition'].")";
				$query66 = "insert into speed_media_recovery_tracking (MediaID,MediaSerialNumber,MediaType,ServerSerialNumber,idPallet,Action,Description,ControllerLoginID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['MediaID'])."','".mysqli_real_escape_string($this->connectionlink,$data['MediaSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['MediaType'])."','".mysqli_real_escape_string($this->connectionlink,$data['ServerSerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$action)."','','".mysqli_real_escape_string($this->connectionlink,$data['AuditController'])."',NOW(),'".$_SESSION['user']['UserId']."')";
				$q66 = mysqli_query($this->connectionlink,$query66);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}			
				//End enter media tracking

				$json['Success'] = true;
				$json['Result'] = "Media Process Completed";
				return json_encode($json);

			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid Next BIN ID";
				return json_encode($json);
			}
			
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}	
	}


	public function GetAllNextBins($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		try {			
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Media')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Media Page';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data
			);	

			$query = "select c.*,d.disposition,d.ssd_disposition,d.destroyed_disposition from custompallet c 
			left join disposition  d on  c.disposition_id = d.disposition_id 
			where c.FacilityID = '".$_SESSION['user']['FacilityID']."' and c.StatusID = '1' and (d.destroyed_disposition = 1 or d.ssd_disposition = 1) order by c.BinName ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$bins = array();
				$i = 0;
				while($row = mysqli_fetch_assoc($q)) {
					$bins[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $bins;				
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "No BINs Available";
				return json_encode($json);
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}	
	}



	public function ProcessMediaSN_Backend($data) {
		

		try {			
			

			$json = array(
				'Success' => false,
				'Result' => $data
			);

			$query = "select * from upload_missing_sns where Completed = 0 limit 10";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				while($row = mysqli_fetch_assoc($q)) {
					$data['MediaSerialNumber'] = $row['MediaSerialNumber'];
					$data['ServerSerialNumber'] = $row['ServerSerialNumber'];
					$data['MediaType'] = 'SSD';
					$data['idPallet'] = $row['RackID'];
					$data['MediaID'] = 0;



					//Start send Custody SNS
					$message_cus = '
					{
						"eventType": "BATCH_MEDIA_DRIVES_IN_CUSTODY",
						"data": {
						"site": "DUB210",
						"login": "jolonm",
						"media": [
							{
							"serial": "'.$data['MediaSerialNumber'].'",
							"type": "SSD",						
							"timestamp": "'.time().'"
							}
							]
						}
					}';
					$event_type_cus = "BATCH_MEDIA_DRIVES_IN_CUSTODY";				
					$SNS_Message_cus = $this->SendSNSMessage($message_cus,$data['MediaSerialNumber'],$event_type_cus,'MEDIA',$data['MediaType'],NULL,$data['idPallet'],NULL,$data['MediaID']);
					if($SNS_Message_cus['Success'] != true) {
						$json['Success'] = false;
						$json['Result'] = 'Custody SNS Message Failed, Holding on Processing Media';
						return json_encode($json);	
					}	
								
					//End send Custody SNS

					//Start calling SNS function

					if(1) { //IF current status is Pending Degauss
						


						$message = '
						{
							"eventType": "BATCH_MEDIA_DRIVES_DESTROYED",
							"data": {
							"site": "DUB210",
							"login": "jolonm",
							"media": [
								{
								"serial": "'.$data['MediaSerialNumber'].'",
								"type": "SSD",
								"binId": "DUB210.PARTS.Destroyed-SC-001",
								"timestamp": "'.time().'"
								}
								]
							}
						}';
						$event_type = "BATCH_MEDIA_DRIVES_DESTROYED";

					} 
					sleep(2);
					
					$SNS_Message = $this->SendSNSMessage($message,$data['MediaSerialNumber'],$event_type,'MEDIA',$data['MediaType'],'DUB210.PARTS.Destroyed-SC-001',$data['idPallet'],NULL,$data['MediaID']);
					if($SNS_Message['Success'] != true) {
						$json['Success'] = false;
						$json['Result'] = 'SNS Message Failed, Holding on Processing Media';
						return json_encode($json);	
					}

					//End calling SNS function

					$query16 = "update upload_missing_sns set Completed = 1 where id = '".mysqli_real_escape_string($this->connectionlink,$row['id'])."'";
					$q16 = mysqli_query($this->connectionlink,$query16);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = "No Serials Available";
				return json_encode($json);
			}

			
			$json['Success'] = true;
			$json['Result'] = "Media Process Completed";
			return json_encode($json);

			
			
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}	
	}


}
?>