<div class="page" data-ng-controller="pending_media">
    <div class="row ui-section">
        <div class="col-md-12">
            <article class="article">


                <script type="text/ng-template" id="password.html">

                    <div style="max-width:767px">

                        <md-toolbar>
                            <div class="md-toolbar-tools">
                            <h2>TPVR for Processing Media (SN : {{CurrentPallet.MediaSerialNumber}})</h2>
                            <span flex></span>
                            <md-button class="md-icon-button" ng-click="cancel()">
                                <md-icon class="material-icons">close</md-icon>
                            </md-button>
                            </div>
                        </md-toolbar>

                        <md-dialog-content>
                            <div class="md-dialog-content">

                                <div class="form-horizontal verification-form">
                                    <form name="tpvForm">
                                        <div class="col-md-4">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Media SN</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.MediaSerialNumber}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Host Asset ID</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.ServerSerialNumber}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Current Status</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.Status}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-8">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Current Disposition</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.disposition}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Next Status</label>
                                                <p class="static_value">
                                                    <strong ng-show="CurrentPallet.Status == 'PendingDegauss'">PendingShred</strong>
                                                    <strong ng-show="CurrentPallet.Status == 'PendingShred'">Shreded</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-input-container class="md-block md-input-has-value">
                                                <label>Media Type</label>
                                                <p class="static_value">
                                                    <strong>{{CurrentPallet.MediaType}}</strong>
                                                </p>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-input-container class="md-block">
                                                <label>Pending Media Controller</label>
                                                <input required name="AuditController" id="AuditController" ng-model="confirmDetails.AuditController" ng-maxlength="50" type="text" autocomplete="off" ng-focus="GetCurrentTime(confirmDetails,'controller_scan_time');" ng-enter="focusNextField('Password')">
                                                <div ng-messages="tpvForm.AuditController.$error" multiple ng-if='tpvForm.AuditController.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="maxlength">Max length 50.</div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                        <div class="col-md-4">
                                            <md-input-container class="md-block">
                                                <label>Password</label>
                                                <input required name="Password" id="Password" ng-model="confirmDetails.Password" ng-maxlength="50" type="password" ng-enter="focusNextField('nextbinid')" autocomplete="off">
                                                <div ng-messages="tpvForm.Password.$error" multiple ng-if='tpvForm.Password.$dirty'>
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="maxlength">Max length 50.</div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                        <!-- <div class="col-md-4">
                                            <md-input-container class="md-block">
                                                <label>Next Bin ID</label>
                                                <input required name="nextbinid" id="nextbinid" style="padding-right: 35px;" ng-model="confirmDetails.nextbinid" ng-maxlength="500" type="text" autocomplete="off" ng-enter="ValidateNextBinID()">
                                                <md-button class="md-fab md-raised md-mini md-accent md-fab-bottom-right" ng-click="ValidateNextBinID()" ng-disabled="!confirmDetails.nextbinid">
                                                    Go
                                                </md-button>
                                                <div ng-messages="tpvForm.NextBinId.$error">
                                                    <div ng-message="required">This is required.</div>
                                                    <div ng-message="maxlength">Max length 50.</div>
                                                </div>
                                            </md-input-container>
                                        </div> -->

                                        <div class="col-md-4">
                                            <md-input-container class="md-block">
                                                <label>Next Bin ID</label>
                                                <md-select name="nextbinid" id="nextbinid" ng-model="confirmDetails.NewCustomPalletID" required ng-enter="ProcessMediaSN($event)" ng-change="GetCurrentTime(confirmDetails,'to_bin_scan_time')">
                                                    <md-option ng-repeat="bin in NextBins" value="{{bin.CustomPalletID}}" ng-show="(bin.ssd_disposition == '1' && CurrentPallet.Status == 'PendingDegauss') || (bin.destroyed_disposition == '1' && CurrentPallet.Status == 'PendingShred')"> {{bin.BinName}} </md-option>
                                                </md-select>
                                                <div class="error-sapce">
                                                    <div ng-messages="tpvForm.NextBinId.$error" multiple ng-if='ticketForm.NextBinId.$dirty'>
                                                        <div ng-message="required">This is required.</div>
                                                    </div>
                                                </div>
                                            </md-input-container>
                                        </div>

                                    </form>
                                </div>

                                <div class="col-md-12 text-center mb-10 mt-10">
                                    <button type="button" style="margin-right:8px;" class="md-button md-raised btn-w-md  md-default" ng-click="cancel()">Close</button>
                                    <!-- <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="hide()" ng-disabled="tpvForm.$invalid">Continue</button> -->
                                    <button type="button" class="md-button md-raised btn-w-md  md-primary" ng-click="ProcessMediaSN($event)" ng-disabled="tpvForm.$invalid">Continue</button>
                                </div>
                        <md-dialog-content>
                    </div>
                </script>


                <!--Pending Media Start-->
                <md-card class="no-margin-h pt-0">

                    <md-toolbar class="md-table-toolbar md-default" ng-init="PendingMediaPannel = true">
                        <div class="md-toolbar-tools" style="cursor: pointer;">
                            <i class="material-icons md-primary" ng-show="PendingMediaPannel" ng-click="PendingMediaPannel = !PendingMediaPannel">keyboard_arrow_up</i>
                            <i class="material-icons md-primary" ng-show="! PendingMediaPannel" ng-click="PendingMediaPannel = !PendingMediaPannel">keyboard_arrow_down</i>
                            <span ng-click="PendingMediaPannel = !PendingMediaPannel">Pending Media</span>
                            <div flex></div>
                            <a ng-click="PendingMediaPannelxls()" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                <md-icon class="excel_icon mr-5" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                            </a>
                        </div>
                    </md-toolbar>

                    <md-card-content ng-show="PendingMediaPannel" class="pt-0" style="padding:0px 16px;">
                        <div class="row">
                            <div class="col-md-12">

                                <div ng-show="Assets" class="pull-right">
                                    <small>
                                    Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                    to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                        <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                    of <span style="font-weight:bold;">{{total}}</span>
                                    </small>
                                </div>
                                <div style="clear:both;"></div>

                                <div class="table-responsive" style="overflow: auto;">
                                    <table class="table table-hover mb-0" md-table md-row-select>
                                        <thead md-head>
                                            <tr class="th_sorting" md-row>
                                                <th md-column>Print</th>
                                                <th md-column><div style="min-width:80px;">Process</div></th>
                                                <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}"><div style="min-width:100px;">Facility<i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>
                                                    <span ng-show="OrderBy == 'FacilityName'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span> </div></th>
                                                <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('CreatedDate')" ng-class="{'orderby' : OrderBy == 'CreatedDate'}"><div style="min-width:180px;">Recovery Date/Time<i class="fa fa-sort pull-right" ng-show="OrderBy != 'CreatedDate'"></i>
                                                    <span ng-show="OrderBy == 'CreatedDate'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span> </div></th>
                                                <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('MediaSerialNumber')" ng-class="{'orderby' : OrderBy == 'MediaSerialNumber'}"><div style="min-width:100px;">Media SN<i class="fa fa-sort pull-right" ng-show="OrderBy != 'MediaSerialNumber'"></i>
                                                    <span ng-show="OrderBy == 'MediaSerialNumber'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span> </div></th>

                                                <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('Status')" ng-class="{'orderby' : OrderBy == 'Status'}"><div style="min-width:100px;">Status<i class="fa fa-sort pull-right" ng-show="OrderBy != 'Status'"></i>
                                                    <span ng-show="OrderBy == 'Status'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span> </div></th>

                                                <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('disposition')" ng-class="{'orderby' : OrderBy == 'disposition'}"><div style="min-width:120px;">Disposition<i class="fa fa-sort pull-right" ng-show="OrderBy != 'disposition'"></i>
                                                    <span ng-show="OrderBy == 'disposition'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span> </div></th>
                                                <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('BinName')" ng-class="{'orderby' : OrderBy == 'BinName'}"><div style="min-width:120px;">Location ID<i class="fa fa-sort pull-right" ng-show="OrderBy != 'BinName'"></i>
                                                    <span ng-show="OrderBy == 'BinName'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span> </div></th>
                                                <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('MediaType')" ng-class="{'orderby' : OrderBy == 'MediaType'}"><div style="min-width:120px;">Media Type<i class="fa fa-sort pull-right" ng-show="OrderBy != 'MediaType'"></i>
                                                    <span ng-show="OrderBy == 'MediaType'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span> </div></th>
                                                <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ServerSerialNumber')" ng-class="{'orderby' : OrderBy == 'ServerSerialNumber'}"><div style="min-width:140px;">Host Asset ID<i class="fa fa-sort pull-right" ng-show="OrderBy != 'ServerSerialNumber'"></i>
                                                    <span ng-show="OrderBy == 'ServerSerialNumber'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span> </div></th>
                                                <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('FirstName')" ng-class="{'orderby' : OrderBy == 'FirstName'}"><div style="min-width:220px;">Media Recovery Login<i class="fa fa-sort pull-right" ng-show="OrderBy != 'FirstName'"></i>
                                                    <span ng-show="OrderBy == 'FirstName'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span> </div></th>
                                                <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('AuditControllerID')" ng-class="{'orderby' : OrderBy == 'AuditControllerID'}"><div style="min-width:220px;">Media Controller Login<i class="fa fa-sort pull-right" ng-show="OrderBy != 'AuditControllerID'"></i>
                                                    <span ng-show="OrderBy == 'AuditControllerID'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span> </div></th>
                                                <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('SiteName')" ng-class="{'orderby' : OrderBy == 'SiteName'}"><div style="min-width:240px;">Media Recovery Station ID<i class="fa fa-sort pull-right" ng-show="OrderBy != 'SiteName'"></i>
                                                    <span ng-show="OrderBy == 'SiteName'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span> </div></th>
                                                <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('idPallet')" ng-class="{'orderby' : OrderBy == 'idPallet'}"><div style="min-width:100px;">Rack ID<i class="fa fa-sort pull-right" ng-show="OrderBy != 'idPallet'"></i>
                                                    <span ng-show="OrderBy == 'idPallet'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span> </div></th>
                                                <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('LoadId')" ng-class="{'orderby' : OrderBy == 'LoadId'}"><div style="min-width:100px;">Ticket ID<i class="fa fa-sort pull-right" ng-show="OrderBy != 'LoadId'"></i>
                                                    <span ng-show="OrderBy == 'LoadId'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span> </div></th>
                                                <th md-column style="cursor:pointer;" ng-click="MakeOrderBy('ReceivedDate')" ng-class="{'orderby' : OrderBy == 'ReceivedDate'}"><div style="min-width:220px;">Rack Receive Date/Time<i class="fa fa-sort pull-right" ng-show="OrderBy != 'ReceivedDate'"></i>
                                                    <span ng-show="OrderBy == 'ReceivedDate'">
                                                        <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                        <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                    </span> </div>
                                                </th>
                                            </tr>

                                            <tr md-row class="errornone">
                                                <td md-cell></td>
                                                <td md-cell></td>
                                                <td md-cell>
                                                    <md-input-container class="md-block mt-0">
                                                        <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()"  aria-label="text" />
                                                    </md-input-container>
                                                </td>
                                                <td md-cell>
                                                    <!-- <md-input-container class="md-block mt-0">
                                                        <input type="text" name="CreatedDate" ng-model="filter_text[0].CreatedDate" ng-change="MakeFilter()"  aria-label="text" />
                                                    </md-input-container> -->
                                                </td>
                                                <td md-cell>
                                                    <md-input-container class="md-block mt-0">
                                                        <input type="text" name="MediaSerialNumber" id="filter_MediaSerialNumber" ng-model="filter_text[0].MediaSerialNumber" ng-change="MakeFilter()"  aria-label="text" />
                                                    </md-input-container>
                                                </td>

                                                <td md-cell>
                                                    <md-input-container class="md-block mt-0">
                                                        <input type="text" name="Status" ng-model="filter_text[0].Status" ng-change="MakeFilter()"  aria-label="text" />
                                                    </md-input-container>
                                                </td>

                                                <td md-cell>
                                                    <md-input-container class="md-block mt-0">
                                                        <input type="text" name="disposition" ng-model="filter_text[0].disposition" ng-change="MakeFilter()"  aria-label="text" />
                                                    </md-input-container>
                                                </td>
                                                <td md-cell>
                                                    <md-input-container class="md-block mt-0">
                                                        <input type="text" name="BinName" ng-model="filter_text[0].BinName" ng-change="MakeFilter()"  aria-label="text" />
                                                    </md-input-container>
                                                </td>
                                                <td md-cell>
                                                    <md-input-container class="md-block mt-0">
                                                        <input type="text" name="MediaType" ng-model="filter_text[0].MediaType" ng-change="MakeFilter()"  aria-label="text" />
                                                    </md-input-container>
                                                </td>
                                                <td md-cell>
                                                    <md-input-container class="md-block mt-0">
                                                        <input type="text" name="ServerSerialNumber" ng-model="filter_text[0].ServerSerialNumber" ng-change="MakeFilter()"  aria-label="text" />
                                                    </md-input-container>
                                                </td>
                                                <td md-cell>
                                                    <md-input-container class="md-block mt-0">
                                                        <input type="text" name="FirstName" ng-model="filter_text[0].FirstName" ng-change="MakeFilter()"  aria-label="text" />
                                                    </md-input-container>
                                                </td>
                                                <td md-cell>
                                                    <md-input-container class="md-block mt-0">
                                                        <input type="text" name="AuditControllerID" ng-model="filter_text[0].AuditControllerID" ng-change="MakeFilter()"  aria-label="text" />
                                                    </md-input-container>
                                                </td>
                                                <td md-cell>
                                                    <md-input-container class="md-block mt-0">
                                                        <input type="text" name="SiteName" ng-model="filter_text[0].SiteName" ng-change="MakeFilter()"  aria-label="text" />
                                                    </md-input-container>
                                                </td>
                                                <td md-cell>
                                                    <md-input-container class="md-block mt-0">
                                                        <input type="text" name="idPallet" ng-model="filter_text[0].idPallet" ng-change="MakeFilter()"  aria-label="text" />
                                                    </md-input-container>
                                                </td>
                                                <td md-cell>
                                                    <md-input-container class="md-block mt-0">
                                                        <input type="text" name="LoadId" ng-model="filter_text[0].LoadId" ng-change="MakeFilter()"  aria-label="text" />
                                                    </md-input-container>
                                                </td>
                                                <td md-cell>
                                                    <!-- <md-input-container class="md-block mt-0">
                                                        <input type="text" name="ReceivedDate" ng-model="filter_text[0].ReceivedDate" ng-change="MakeFilter()"  aria-label="text" />
                                                    </md-input-container> -->
                                                </td>
                                            </tr>

                                        </thead>
                                        <tbody>
                                            <tr ng-repeat="asset in Assets" ng-class="{'danger' : GetHoursDifference(asset.CreatedDate) >= 2,'warning' : GetHoursDifference(asset.CreatedDate) >= 1 && GetHoursDifference(asset.CreatedDate) < 2}">
                                                <td md-cell class="actionicons" style="min-width: 60px;">
                                                    <a href="{{host}}label/master/examples/medialabel.php?serial={{asset.MediaSerialNumber}}" target="_blank">
                                                        <i class="material-icons print" role="img" aria-label="print">print</i>
                                                    </a>
                                                </td>
                                                <td md-cell>
                                                    <a class="text-success" style="cursor: pointer;" ng-click="ValidateMediaProcessTPVRControllerPopup(asset,$event)"><strong>Process</strong></a>
                                                </td>
                                                <td md-cell>{{asset.FacilityName}}</td>
                                                <td md-cell >{{asset.CreatedDate | toDate | date:'MMM dd, yyyy hh:mm a'}} <strong>(<span am-time-ago="asset.CreatedDate"></span>)</strong>
                                                    <!-- {{GetHoursDifference(asset.CreatedDate)}}                                                 -->
                                                    <!-- ({{asset.CreatedDate | amDurationFormat : 'minuit'}}) -->
                                                </td>
                                                <td md-cell>{{asset.MediaSerialNumber}}</td>
                                                <td md-cell>{{asset.Status}}</td>
                                                <td md-cell>{{asset.disposition}}</td>
                                                <td md-cell>{{asset.BinName}} </td>
                                                <td md-cell>{{asset.MediaType}}</td>
                                                <td md-cell>{{asset.ServerSerialNumber}}</td>
                                                <td md-cell>{{asset.FirstName}} {{asset.LastName}}</td>
                                                <td md-cell>{{asset.AuditControllerID}}</td>
                                                <td md-cell>{{asset.SiteName}}</td>
                                                <td md-cell>{{asset.idPallet}}</td>
                                                <td md-cell>{{asset.LoadId}}</td>
                                                <td md-cell>{{asset.ReceivedDate | toDate | date:'MMM dd, yyyy hh:mm a'}}</td>
                                            </tr>
                                        </tbody>

                                        <tfoot>
                                            <tr>
                                                <td colspan="7">
                                                    <div>
                                                        <ul class="pagination">
                                                            <li ng-class="prevPageDisabled()">
                                                                <a href ng-click="firstPage()"><< First</a>
                                                            </li>
                                                            <li ng-class="prevPageDisabled()">
                                                                <a href ng-click="prevPage()"><< Prev</a>
                                                            </li>
                                                            <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                <a style="cursor:pointer;">{{n+1}}</a>
                                                            </li>
                                                            <li ng-class="nextPageDisabled()">
                                                                <a href ng-click="nextPage()">Next >></a>
                                                            </li>
                                                            <li ng-class="nextPageDisabled()">
                                                                <a href ng-click="lastPage()">Last >></a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tfoot>

                                    </table>
                                </div>

                            </div>
                        </div>

                    </md-card-content>

                </md-card>
                <!--Pending Media End-->

            </article>
        </div>
    </div>
</div>